#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复768维向量表问题
专门为本地模型创建正确的768维向量表
"""

import asyncio
import asyncpg
import sys

async def fix_768_dimensions():
    """修复768维向量表"""
    print("🔧 修复768维向量表问题...")
    
    try:
        # 连接到数据库
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 1. 删除现有的向量表
        print("🗑️  删除现有向量表...")
        await conn.execute("DROP TABLE IF EXISTS vector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS multivector_embeddings CASCADE;")
        print("✅ 已删除现有向量表")
        
        # 2. 创建768维向量表
        print("🏗️  创建768维向量表...")
        
        await conn.execute("""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(768) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        await conn.execute("""
            CREATE TABLE multivector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(768) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        print("✅ 768维向量表创建成功")
        
        # 3. 创建索引
        print("📊 创建索引...")
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_document_id 
            ON vector_embeddings(document_id);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_document_id 
            ON multivector_embeddings(document_id);
        """)
        
        print("✅ 索引创建成功")
        
        # 4. 更新元数据表
        print("📝 更新元数据...")
        await conn.execute("""
            INSERT INTO vector_store_metadata (key, value) 
            VALUES ('dimensions', '768')
            ON CONFLICT (key) DO UPDATE SET value = '768';
        """)
        
        print("✅ 元数据更新成功")
        
        await conn.close()
        
        print("\n🎉 768维向量表修复完成！")
        print("💡 现在可以正常使用本地模型了")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        return False

async def main():
    """主函数"""
    print("Morphik 768维向量表修复工具")
    print("=" * 40)
    print("💡 此工具将为本地模型创建正确的768维向量表")
    print()
    
    success = await fix_768_dimensions()
    
    if success:
        print("\n🎉 修复成功！")
        print("📋 接下来的步骤:")
        print("1. 重启 Morphik 服务")
        print("2. 测试本地模型功能")
        print()
        print("命令:")
        print("  cd .. && ./manage_services.sh restart")
        print("  cd code && python 1.py")
    else:
        print("\n❌ 修复失败")
        print("请检查数据库连接和权限")

if __name__ == "__main__":
    asyncio.run(main())
