repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v2.3.0
    hooks:
    -   id: check-yaml
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
-   repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
    -   id: isort
        name: isort (python)
        args: [--profile=black, --line-length=120]
-   repo: https://github.com/psf/black
    rev: 24.4.2
    hooks:
    -   id: black
        language_version: python3.11
        args: [--line-length=120]
-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.6
    hooks:
    -   id: ruff
        # Fix issues but skip import-sorting rules (I) to avoid conflicts with isort
        args: [--fix, --ignore=I]
