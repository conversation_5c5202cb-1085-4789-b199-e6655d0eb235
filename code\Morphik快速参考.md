# Morphik 快速参考卡片

## 🚀 一键启动
```bash
# 启动所有服务
./manage_services.sh start-all

# 检查状态
./manage_services.sh status
```

## 🛑 服务管理
```bash
./manage_services.sh start-all    # 启动所有服务
./manage_services.sh start-base   # 启动基础服务
./manage_services.sh start-api    # 启动 API 服务
./manage_services.sh stop         # 停止所有服务
./manage_services.sh restart      # 重启所有服务
./manage_services.sh status       # 查看状态
./manage_services.sh logs         # 查看日志
```

## 🌐 服务地址
| 服务 | 地址 | 端口 |
|------|------|------|
| Morphik API | http://localhost:8000 | 8000 |
| API 文档 | http://localhost:8000/docs | 8000 |
| 健康检查 | http://localhost:8000/health | 8000 |
| PostgreSQL | localhost:5433 | 5433 |
| Redis | localhost:6379 | 6379 |
| Ollama | http://localhost:11435 | 11435 |

## 🧪 快速测试
```bash
# 健康检查
curl http://localhost:8000/health

# 聊天测试
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "你好", "model": "qwen2.5"}'

# 查看配置
curl http://localhost:8000/config
```

## 🔧 故障排除
```bash
# 1. 检查 Docker 状态
docker info

# 2. 查看容器状态
docker compose ps

# 3. 查看端口占用
lsof -i :8000
lsof -i :5433
lsof -i :6379
lsof -i :11435

# 4. 重启服务
./manage_services.sh restart

# 5. 查看日志
./manage_services.sh logs
```

## 📦 模型管理
```bash
# 查看已安装模型
curl http://localhost:11435/api/tags

# 下载模型
curl -X POST http://localhost:11435/api/pull -d '{"name": "qwen2.5"}'
curl -X POST http://localhost:11435/api/pull -d '{"name": "nomic-embed-text:latest"}'

# 删除模型
curl -X DELETE http://localhost:11435/api/delete -d '{"name": "model-name"}'
```

## 🗄️ 数据库操作
```bash
# 连接数据库
docker exec -it morphik-core_main-postgres-1 psql -U morphik -d morphik

# 备份数据库
docker exec morphik-core_main-postgres-1 pg_dump -U morphik morphik > backup.sql

# 恢复数据库
docker exec -i morphik-core_main-postgres-1 psql -U morphik morphik < backup.sql
```

## 🧹 清理命令
```bash
# 停止所有服务
./manage_services.sh stop

# 删除容器和数据
docker compose down -v

# 清理 Docker 资源
docker system prune -f

# 清理所有镜像（谨慎使用）
docker system prune -a
```

## ⚙️ 配置文件
- **服务管理**: `manage_services.sh`
- **Docker 配置**: `docker-compose.yml`
- **应用配置**: `morphik.toml`
- **环境变量**: `.env`
- **简化 API**: `simple_morphik_app.py`

## 🆘 常见错误
| 错误 | 解决方案 |
|------|----------|
| Docker 未启动 | 启动 Docker Desktop |
| 端口被占用 | `./manage_services.sh stop` |
| 模型下载失败 | 检查网络，重新下载 |
| 数据库连接失败 | `./manage_services.sh restart` |
| API 无响应 | 检查日志，重启服务 |

## 📞 支持
1. 查看状态: `./manage_services.sh status`
2. 查看日志: `./manage_services.sh logs`
3. 重启服务: `./manage_services.sh restart`
4. 查看完整教程: `code/Morphik服务启动教程.md`
