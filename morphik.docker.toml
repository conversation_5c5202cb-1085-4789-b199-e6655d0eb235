# Morphik Docker 环境配置文件
# 专门用于 Docker Compose 部署

[auth]
jwt_algorithm = "HS256"
dev_mode = true  # Enabled by default for easier local development
dev_entity_id = "dev_user"  # Default dev user ID
dev_entity_type = "developer"  # Default dev entity type
dev_permissions = ["read", "write", "admin"]  # Default dev permissions

#### Registered models ####
[registered_models]
# OpenAI models
openai_gpt4 = { model_name = "gpt-4" }
openai_gpt4o = { model_name = "gpt-4o" }
openai_gpt4o_mini = { model_name = "gpt-4o-mini" }
openai_gpt35_turbo = { model_name = "gpt-3.5-turbo" }

# Claude models
claude_sonnet = { model_name = "claude-3-5-sonnet-20241022" }
claude_haiku = { model_name = "claude-3-5-haiku-20241022" }
claude_opus = { model_name = "claude-3-opus-20240229" }

# Ollama models (本机 Ollama - 通过 host.docker.internal 访问)
ollama_qwen3_4b = { model_name = "ollama_chat/qwen3:4b", api_base = "http://host.docker.internal:11434" }

# Embedding models
openai_embedding = { model_name = "text-embedding-3-small" }
openai_embedding_large = { model_name = "text-embedding-3-large" }
ollama_embedding_local = { model_name = "ollama/nomic-embed-text:latest", api_base = "http://host.docker.internal:11434" }

#### Component configurations ####

[agent]
model = "ollama_qwen3_4b" # Model for the agent logic

[completion]
model = "ollama_qwen3_4b" # Reference to a key in registered_models
default_max_tokens = "1000"
default_temperature = 0.5

[database]
provider = "postgres"
# Connection pool settings
pool_size = 10           # Maximum number of connections in the pool
max_overflow = 15        # Maximum number of connections that can be created beyond pool_size
pool_recycle = 3600      # Time in seconds after which a connection is recycled (1 hour)
pool_timeout = 10        # Seconds to wait for a connection from the pool
pool_pre_ping = true     # Check connection viability before using it from the pool
max_retries = 3          # Number of retries for database operations
retry_delay = 1.0        # Initial delay between retries in seconds

[embedding]
model = "ollama_embedding_local"  # Reference to registered model
dimensions = 768
similarity_metric = "cosine"

[parser]
chunk_size = 6000
chunk_overlap = 300
use_unstructured_api = false
use_contextual_chunking = false
contextual_chunking_model = "ollama_qwen3_4b"  # Reference to a key in registered_models

[document_analysis]
model = "ollama_qwen3_4b"  # Reference to a key in registered_models

[parser.vision]
model = "ollama_qwen3_4b"  # Reference to a key in registered_models
frame_sample_rate = -1  # Set to -1 to disable frame captioning

[reranker]
use_reranker = false  # 在 Docker 中禁用 reranker 以减少资源使用
provider = "flag"
model_name = "BAAI/bge-reranker-large"
query_max_length = 256
passage_max_length = 512
use_fp16 = true
device = "cpu" # 在 Docker 中使用 CPU

[storage]
provider = "local"
storage_path = "./storage"

[vector_store]
provider = "pgvector"

[rules]
model = "ollama_qwen3_4b"
batch_size = 4096

[morphik]
enable_colpali = false  # 在 Docker 中禁用 ColPali 以减少复杂性
mode = "self_hosted"  # "cloud" or "self_hosted"
api_domain = "api.morphik.ai"  # API domain for cloud URIs
colpali_mode = "off" # "off", "local", or "api"

[redis]
host = "redis"  # Docker 服务名
port = 6379

[graph]
model = "ollama_qwen3_4b"
enable_entity_resolution = true

[telemetry]
enabled = false  # 在开发环境中禁用遥测
honeycomb_enabled = false
