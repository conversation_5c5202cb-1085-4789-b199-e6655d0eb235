#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Morphik 文档诊断脚本
用于检查数据库中的文档状态，排查文件名查找问题
"""

from morphik import Morphik

def diagnose_documents():
    """诊断数据库中的文档状态"""
    print("🔍 Morphik 文档诊断工具")
    print("=" * 50)
    
    try:
        # 连接到 Morphik
        db = Morphik(is_local=True, timeout=120)
        print("✅ 已连接到 Morphik 服务器")
        
        # 1. 列出所有文档
        print("\n📋 所有文档列表:")
        all_docs = db.list_documents()
        print(f"数据库中共有 {len(all_docs)} 个文档")
        
        for i, doc in enumerate(all_docs):
            print(f"\n文档 {i+1}:")
            print(f"  ID: {doc.external_id}")
            print(f"  文件名: {doc.filename}")
            print(f"  元数据: {doc.metadata}")
        
        # 2. 检查医疗领域文档
        print("\n🏥 医疗领域文档:")
        medical_docs = db.list_documents(filters={"领域": "医疗"})
        print(f"医疗领域共有 {len(medical_docs)} 个文档")
        
        for i, doc in enumerate(medical_docs):
            print(f"\n医疗文档 {i+1}:")
            print(f"  ID: {doc.external_id}")
            print(f"  文件名: {doc.filename}")
            print(f"  元数据: {doc.metadata}")
        
        # 3. 尝试查找特定文件名
        print("\n🔍 文件名查找测试:")
        test_filenames = [
            "ai_in_healthcare.md",
            "ai_in_healthcare_fallback.md",
            "simple_doc.txt",
            "metadata_doc.txt"
        ]
        
        for filename in test_filenames:
            try:
                doc = db.get_document_by_filename(filename)
                if doc:
                    print(f"  ✅ 找到文件: {filename} -> ID: {doc.external_id}")
                else:
                    print(f"  ❌ 未找到文件: {filename}")
            except Exception as e:
                print(f"  ❌ 查找文件 '{filename}' 时出错: {e}")
        
        # 4. 检查文件名模式
        print("\n📊 文件名统计:")
        filename_patterns = {}
        for doc in all_docs:
            if doc.filename:
                if doc.filename in filename_patterns:
                    filename_patterns[doc.filename] += 1
                else:
                    filename_patterns[doc.filename] = 1
        
        for filename, count in filename_patterns.items():
            print(f"  {filename}: {count} 个文档")
        
        # 5. 检查重复文件名
        print("\n⚠️  重复文件名检查:")
        duplicates = {k: v for k, v in filename_patterns.items() if v > 1}
        if duplicates:
            print("发现重复文件名:")
            for filename, count in duplicates.items():
                print(f"  {filename}: {count} 个重复")
        else:
            print("  没有发现重复文件名")
        
        db.close()
        print("\n✅ 诊断完成")
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")

if __name__ == "__main__":
    diagnose_documents()
