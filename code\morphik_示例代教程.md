# Morphik Python SDK 完整教程

本教程提供了对 Morphik Python SDK 的全面介绍，从基础功能到高级特性，循序渐进地展示如何使用 Morphik 构建智能文档处理和知识管理应用。

## 教程结构

教程分为三个主要部分，每个部分都有对应的 Python 脚本文件：

1. **基础功能** (`morphik_tutorial_part1.py`)
   - 连接与初始化
   - 文档摄取（文本和文件）
   - 基础检索与查询
   - 文档管理（列表、获取、更新、删除）

2. **高级功能** (`morphik_tutorial_part2.py`)
   - 知识图谱创建与查询
   - 多模态搜索（ColPali）
   - 用户和文件夹管理
   - 缓存增强生成（Cache-Augmented-Generation）

3. **大语言模型配置与实际应用** (`morphik_tutorial_part3.py`)
   - 大语言模型配置（在线和本地模型）
   - 高级查询与提示词覆盖
   - 实际应用场景：智能文档分析系统

## 准备工作

### 安装 Morphik

首先，确保已安装 Morphik Python SDK：

```bash
pip install morphik
```

### 环境设置

根据您的使用场景，设置相应的环境变量：

```python
# 对于在线服务
import os
os.environ["OPENAI_API_KEY"] = "your-openai-api-key"  # 如果使用 OpenAI
os.environ["ANTHROPIC_API_KEY"] = "your-anthropic-api-key"  # 如果使用 Anthropic

# 对于本地服务
# 确保 Morphik 服务器已在本地启动（默认地址：localhost:8000）
```

## 快速入门

以下是一个简单的示例，展示 Morphik 的基本用法：

```python
from morphik import Morphik

# 初始化 Morphik 客户端（本地模式）
db = Morphik(is_local=True)

# 摄取文本文档
doc = db.ingest_text(
    content="这是一个示例文档，用于演示 Morphik 的基本功能。",
    filename="example.txt",
    metadata={"类型": "示例", "作者": "Morphik 用户"}
)

# 查询文档
response = db.query("这个文档的主要内容是什么？")
print(response.completion)

# 关闭连接
db.close()
```

## 教程内容概览

### 第一部分：基础功能

第一部分介绍 Morphik 的基础功能，包括：

1. **连接与初始化**
   - 本地连接
   - 远程连接（带认证）
   - 使用上下文管理器

2. **文档摄取**
   - 基础文本摄取
   - 带元数据的文本摄取
   - 使用规则的文本摄取
   - 文件摄取

3. **基础检索与查询**
   - 检索文档块
   - 使用元数据过滤
   - 基本查询（RAG）
   - 结构化输出查询

4. **文档管理**
   - 列出文档
   - 获取文档
   - 更新文档元数据和内容
   - 删除文档

### 第二部分：高级功能

第二部分深入探讨 Morphik 的高级功能：

1. **知识图谱创建与查询**
   - 基本知识图谱创建
   - 自定义知识图谱（提示词覆盖和实体示例）
   - 基于知识图谱的查询
   - 图谱路径分析

2. **多模态搜索（ColPali）**
   - 多模态文档摄取
   - 多模态检索
   - 多模态查询

3. **用户和文件夹管理**
   - 文件夹创建和使用
   - 用户作用域
   - 组合用户和文件夹

4. **缓存增强生成**
   - 创建缓存
   - 查询缓存
   - 添加文档到缓存
   - 更新缓存

### 第三部分：大语言模型配置与实际应用

第三部分介绍大语言模型配置和实际应用场景：

1. **大语言模型配置**
   - 在线大模型配置（OpenAI、Anthropic）
   - 本地 Ollama 模型配置
   - 自定义模型服务
   - 动态切换模型

2. **高级查询与提示词覆盖**
   - 自定义查询提示词
   - 自定义检索提示词
   - 完全自定义提示词

3. **实际应用场景：智能文档分析系统**
   - 系统初始化
   - 文档摄取与处理
   - 知识图谱构建
   - 多角度分析
   - 生成结构化报告

## 运行教程

每个部分的教程都可以独立运行。建议按顺序学习，从基础到高级：

```bash
# 运行第一部分：基础功能
python morphik_tutorial_part1.py

# 运行第二部分：高级功能
python morphik_tutorial_part2.py

# 运行第三部分：大语言模型配置与实际应用
python morphik_tutorial_part3.py
```

## 大语言模型配置详解

Morphik 支持多种大语言模型配置，包括在线 API 服务和本地部署模型。

### 在线大模型配置

#### OpenAI 配置

```python
from morphik import Morphik
from morphik.models import LLMConfig, OpenAIConfig

# 设置 OpenAI API 密钥
os.environ["OPENAI_API_KEY"] = "your-openai-api-key"

# 创建 OpenAI 配置
openai_config = OpenAIConfig(
    model="gpt-4",  # 模型名称：gpt-4, gpt-4-turbo, gpt-3.5-turbo 等
    temperature=0.7,  # 生成的随机性：0.0-1.0，值越低结果越确定
    max_tokens=500,  # 最大生成令牌数
    top_p=0.95,  # 核采样参数：控制输出的多样性
    frequency_penalty=0.0,  # 频率惩罚：降低重复内容的可能性
    presence_penalty=0.0  # 存在惩罚：增加新主题出现的可能性
)

# 初始化 Morphik 客户端并配置 LLM
db = Morphik(
    llm_config=LLMConfig(
        provider="openai",  # 提供商名称
        config=openai_config  # 提供商特定配置
    )
)
```

#### Anthropic 配置

```python
from morphik import Morphik
from morphik.models import LLMConfig, AnthropicConfig

# 设置 Anthropic API 密钥
os.environ["ANTHROPIC_API_KEY"] = "your-anthropic-api-key"

# 创建 Anthropic 配置
anthropic_config = AnthropicConfig(
    model="claude-3-opus-20240229",  # 模型名称：claude-3-opus, claude-3-sonnet, claude-3-haiku 等
    temperature=0.5,  # 生成的随机性
    max_tokens=1000,  # 最大生成令牌数
    top_p=0.9  # 核采样参数
)

# 初始化 Morphik 客户端并配置 LLM
db = Morphik(
    llm_config=LLMConfig(
        provider="anthropic",
        config=anthropic_config
    )
)
```

### 本地 Ollama 模型配置

使用 [Ollama](https://ollama.ai/) 运行本地大语言模型：

```python
from morphik import Morphik
from morphik.models import LLMConfig, OllamaConfig

# 创建 Ollama 配置
ollama_config = OllamaConfig(
    model="llama2",  # 模型名称（确保已在 Ollama 中拉取）
    temperature=0.7,  # 生成的随机性
    max_tokens=500,  # 最大生成令牌数
    top_p=0.9,  # 核采样参数
    host="http://localhost:11434"  # Ollama 服务地址（默认端口为 11434）
)

# 初始化 Morphik 客户端并配置 LLM
db = Morphik(
    llm_config=LLMConfig(
        provider="ollama",
        config=ollama_config
    )
)
```

使用量化版 Ollama 模型（更小，速度更快）：

```python
# 创建量化版 Ollama 配置
ollama_quantized_config = OllamaConfig(
    model="llama2:7b-q4_0",  # 量化模型名称，q4_0 表示 4 位量化
    temperature=0.7,
    max_tokens=500,
    host="http://localhost:11434"
)

# 初始化 Morphik 客户端并配置 LLM
db = Morphik(
    llm_config=LLMConfig(
        provider="ollama",
        config=ollama_quantized_config
    )
)
```

### 动态切换模型

在应用程序运行期间，您可以根据需要动态切换不同的模型：

```python
# 初始化默认客户端（使用 OpenAI）
db = Morphik(
    llm_config=LLMConfig(
        provider="openai",
        config=OpenAIConfig(model="gpt-4")
    )
)

# 使用默认模型进行查询
standard_response = db.query(
    "总结人工智能的主要应用领域"
)

# 对于复杂分析，使用 Claude 模型
complex_response = db.query(
    "分析量子计算对密码学的潜在影响，包括优势和风险",
    llm_config=LLMConfig(  # 在查询时覆盖默认配置
        provider="anthropic",
        config=AnthropicConfig(
            model="claude-3-opus-20240229",
            temperature=0.3  # 降低温度以获得更确定的分析
        )
    )
)

# 对于敏感数据查询，切换到本地 Ollama 模型
sensitive_response = db.query(
    "分析公司内部财务数据趋势",
    llm_config=LLMConfig(
        provider="ollama",
        config=OllamaConfig(
            model="llama2",
            temperature=0.2
        )
    )
)
```

## 最佳实践

### 文档摄取

- 为文档添加丰富的元数据，以便更精确地过滤和检索
- 使用元数据提取规则自动从文档内容中提取结构化信息
- 对于长文档，考虑使用自然语言规则进行预处理

### 查询优化

- 使用适当的 `k` 值（检索的文档块数量）平衡相关性和上下文广度
- 调整 `temperature` 参数控制生成的随机性
- 使用提示词覆盖自定义检索和生成行为
- 对于特定领域的查询，使用专门的知识图谱

### 模型选择

- 对于敏感数据处理，优先使用本地模型如 Ollama
- 对于需要高质量结果的场景，考虑使用 OpenAI 或 Anthropic 的高级模型
- 根据任务复杂度选择适当的模型大小和参数

### 性能优化

- 使用缓存增强生成减少重复计算
- 对于高频查询模式，创建专用缓存
- 使用文件夹和用户作用域组织和隔离数据

## 结语

通过本教程，您已经了解了 Morphik Python SDK 的全部主要功能，从基础的文档摄取和查询，到高级的知识图谱和多模态搜索，再到灵活的大语言模型配置和实际应用场景。

Morphik 提供了构建智能文档处理和知识管理应用所需的全部工具。无论您是构建企业知识库、研究助手、内容分析系统还是智能客户服务，Morphik 都能满足您的需求。

现在，您已经准备好开始构建自己的智能应用了！

## 参考资源

- [Morphik 官方文档](https://www.morphik.ai/docs/introduction)
- [Morphik Python SDK 参考](https://www.morphik.ai/docs/python-sdk/morphik)
- [Ollama 官方网站](https://ollama.ai/)
- [OpenAI API 文档](https://platform.openai.com/docs/api-reference)
- [Anthropic API 文档](https://docs.anthropic.com/claude/reference/getting-started-with-the-api)
