#!/usr/bin/env python3
"""
Morphik 本地启动脚本
简化版本，用于快速启动 Morphik API 服务
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    print(f"🐍 Python 版本: {version.major}.{version.minor}.{version.micro}")
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ 错误: 需要 Python 3.9 或更高版本")
        sys.exit(1)
    return True

def install_core_dependencies():
    """安装核心依赖"""
    print("📦 安装核心依赖...")
    
    core_deps = [
        "fastapi>=0.100.0",
        "uvicorn>=0.20.0",
        "sqlalchemy>=2.0.0",
        "psycopg2-binary>=2.9.0",
        "redis>=4.0.0",
        "pydantic>=2.0.0",
        "python-multipart>=0.0.6",
        "requests>=2.28.0",
        "python-dotenv>=1.0.0",
        "toml>=0.10.0",
    ]
    
    for dep in core_deps:
        try:
            print(f"   安装 {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"   ⚠️  警告: 无法安装 {dep}: {e}")
    
    print("✅ 核心依赖安装完成")

def check_services():
    """检查必要的服务"""
    print("🔍 检查服务状态...")
    
    # 检查 PostgreSQL
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=5433,
            database="morphik",
            user="morphik",
            password="morphik"
        )
        conn.close()
        print("   ✅ PostgreSQL 连接成功")
    except Exception as e:
        print(f"   ❌ PostgreSQL 连接失败: {e}")
        return False
    
    # 检查 Ollama
    try:
        response = requests.get("http://localhost:11435/api/tags", timeout=5)
        if response.status_code == 200:
            print("   ✅ Ollama 服务正常")
        else:
            print(f"   ⚠️  Ollama 服务状态异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Ollama 服务连接失败: {e}")
        return False
    
    return True

def create_simple_app():
    """创建简化的 FastAPI 应用"""
    app_code = '''
import os
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
from pydantic import BaseModel
import toml
from pathlib import Path

app = FastAPI(title="Morphik API", version="0.1.0")

# 加载配置
config_path = Path("morphik.toml")
if config_path.exists():
    config = toml.load(config_path)
else:
    config = {
        "api": {"host": "0.0.0.0", "port": 8000},
        "database": {"provider": "postgres"},
        "completion": {"provider": "ollama", "model_name": "qwen2.5:4b"},
        "embedding": {"provider": "ollama", "model_name": "nomic-embed-text:latest"}
    }

@app.get("/")
async def root():
    return {"message": "Morphik API is running!", "version": "0.1.0"}

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "services": {
            "api": "running",
            "database": "connected",
            "ollama": "available"
        }
    }

@app.get("/config")
async def get_config():
    """获取当前配置"""
    return config

class ChatRequest(BaseModel):
    message: str
    model: str = "qwen2.5:4b"

@app.post("/chat")
async def chat(request: ChatRequest):
    """简单的聊天端点"""
    try:
        import requests
        
        ollama_url = "http://localhost:11435/api/generate"
        payload = {
            "model": request.model,
            "prompt": request.message,
            "stream": False
        }
        
        response = requests.post(ollama_url, json=payload, timeout=30)
        if response.status_code == 200:
            result = response.json()
            return {"response": result.get("response", "No response")}
        else:
            raise HTTPException(status_code=500, detail="Ollama service error")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")

if __name__ == "__main__":
    host = config.get("api", {}).get("host", "0.0.0.0")
    port = config.get("api", {}).get("port", 8000)
    uvicorn.run(app, host=host, port=port)
'''
    
    with open("simple_morphik_app.py", "w", encoding="utf-8") as f:
        f.write(app_code)
    
    print("✅ 简化应用创建完成")

def main():
    """主函数"""
    print("🚀 Morphik 本地启动器")
    print("=" * 50)
    
    # 检查 Python 版本
    check_python_version()
    
    # 安装核心依赖
    install_core_dependencies()
    
    # 检查服务
    if not check_services():
        print("❌ 服务检查失败，请确保 PostgreSQL 和 Ollama 正在运行")
        print("💡 提示:")
        print("   - PostgreSQL: docker compose up -d postgres")
        print("   - Ollama: docker compose --profile ollama up -d ollama")
        sys.exit(1)
    
    # 创建简化应用
    create_simple_app()
    
    print("\n🎉 准备就绪！")
    print("📋 启动命令:")
    print("   python3 simple_morphik_app.py")
    print("\n🌐 访问地址:")
    print("   - API: http://localhost:8000")
    print("   - 健康检查: http://localhost:8000/health")
    print("   - 配置信息: http://localhost:8000/config")
    print("   - 聊天测试: POST http://localhost:8000/chat")
    
    # 询问是否立即启动
    try:
        start_now = input("\n🤔 是否立即启动服务? (y/n): ").lower().strip()
        if start_now in ['y', 'yes', '是']:
            print("🚀 启动 Morphik API...")
            subprocess.run([sys.executable, "simple_morphik_app.py"])
    except KeyboardInterrupt:
        print("\n👋 再见！")

if __name__ == "__main__":
    main()
