#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Morphik 教程运行器 - 扩展超时版本
专门用于运行知识图谱相关的教程，使用扩展的超时时间和更好的错误处理
"""

import time
import sys
from morphik import Morphik

def run_knowledge_graph_tutorial():
    """运行知识图谱教程，使用扩展超时时间"""
    print("🎓 Morphik 知识图谱教程 - 扩展超时版本")
    print("=" * 60)
    print("💡 此版本使用了扩展的超时时间来处理复杂的知识图谱操作")
    print("⏰ 预计总耗时：10-15分钟（包括图谱构建和查询）")
    print()

    try:
        # 使用扩展超时时间连接（10分钟）
        print("🔗 连接到 Morphik（超时时间：600秒）...")
        db = Morphik(is_local=True, timeout=600)
        print("✅ 连接成功")

        # === 1. 文档摄取 ===
        print("\n📝 === 1. 文档摄取 ===")

        # 摄取技术文档
        tech_doc = db.ingest_text(
            content="""
            人工智能（AI）是计算机科学的一个分支，致力于创建能够模拟人类智能的系统。
            机器学习是AI的一个子领域，专注于开发能从数据中学习的算法。
            深度学习是机器学习的一种特殊形式，使用神经网络进行学习。

            谷歌的AlphaGo是一个著名的AI系统，它在2016年击败了围棋世界冠军李世石。
            AlphaGo由DeepMind开发，DeepMind是谷歌在2014年收购的AI研究公司。
            """,
            filename="ai_overview.txt",
            metadata={"领域": "技术", "主题": "人工智能"}
        )
        print(f"✅ 技术文档摄取成功，ID: {tech_doc.external_id}")

        # 摄取公司文档
        company_doc = db.ingest_text(
            content="""
            OpenAI是一家人工智能研究公司，成立于2015年。
            OpenAI开发了GPT系列模型，包括GPT-3和GPT-4，这些是大型语言模型。
            ChatGPT是基于GPT模型的对话AI系统，于2022年发布。

            微软与OpenAI建立了战略合作伙伴关系，并在Azure云平台上提供OpenAI的服务。
            """,
            filename="companies.txt",
            metadata={"领域": "商业", "主题": "AI公司"}
        )
        print(f"✅ 公司文档摄取成功，ID: {company_doc.external_id}")

        # 等待文档索引
        print("⏳ 等待文档索引完成...")
        time.sleep(5)

        # === 2. 创建知识图谱 ===
        print("\n🏗️  === 2. 创建知识图谱 ===")

        graph_name = f"tutorial_graph_{int(time.time())}"
        print(f"📊 创建知识图谱: {graph_name}")
        print("💡 这个过程可能需要几分钟，请耐心等待...")

        start_time = time.time()

        try:
            # 创建图谱
            graph = db.create_graph(
                name=graph_name,
                filters={"领域": ["技术", "商业"]}
            )

            create_time = time.time() - start_time
            print(f"✅ 图谱创建请求成功！耗时: {create_time:.2f}秒")

            # 等待图谱处理完成（使用扩展超时）
            print("⏳ 等待图谱处理完成（最多等待10分钟）...")
            wait_start_time = time.time()

            completed_graph = db.wait_for_graph_completion(
                graph_name,
                timeout_seconds=600,  # 10分钟超时
                check_interval_seconds=10  # 每10秒检查一次
            )

            wait_time = time.time() - wait_start_time
            total_time = time.time() - start_time

            print(f"✅ 图谱处理完成！")
            print(f"   - 等待时间: {wait_time:.2f}秒")
            print(f"   - 总耗时: {total_time:.2f}秒")
            print(f"📊 图谱统计:")
            print(f"   - 实体数量: {len(completed_graph.entities)}")
            print(f"   - 关系数量: {len(completed_graph.relationships)}")
            print(f"   - 文档数量: {len(completed_graph.document_ids)}")

            # 显示实体示例
            if completed_graph.entities:
                print(f"\n🔍 实体示例（前5个）:")
                for i, entity in enumerate(completed_graph.entities[:5]):
                    print(f"   {i+1}. {entity.label} (类型: {entity.type})")
            else:
                print("\n⚠️  未找到实体，图谱可能为空")

            # 显示关系示例
            if completed_graph.relationships:
                print(f"\n🔗 关系示例（前5个）:")
                for i, rel in enumerate(completed_graph.relationships[:5]):
                    print(f"   {i+1}. {rel.source_id} -> {rel.target_id} ({rel.type})")
            else:
                print("\n⚠️  未找到关系，图谱可能为空")

        except Exception as e:
            total_time = time.time() - start_time
            print(f"❌ 图谱创建/处理失败，总耗时: {total_time:.2f}秒")
            print(f"错误信息: {e}")
            print("💡 这可能是由于超时、模型问题或配置问题导致的")
            return False

        # === 3. 使用知识图谱查询 ===
        print("\n🔍 === 3. 使用知识图谱查询 ===")

        try:
            query_start_time = time.time()

            # 基于知识图谱的查询
            response = db.query(
                query="解释人工智能、机器学习和深度学习之间的关系",
                graph_name=graph_name,
                hop_depth=2,
                include_paths=True,
                temperature=0.7
            )

            query_time = time.time() - query_start_time
            print(f"✅ 图谱查询成功！耗时: {query_time:.2f}秒")
            print(f"📄 查询结果:")
            print(response.completion)

            print(f"\n📚 使用的信息源:")
            for i, source in enumerate(response.sources):
                print(f"   {i+1}. 文档 {source.document_id}, 块 {source.chunk_number}")

            # 显示图谱路径（如果有）
            if response.metadata and "graph" in response.metadata:
                print(f"\n🛤️  图谱路径:")
                for path in response.metadata["graph"]["paths"]:
                    print(f"   {' -> '.join(path)}")

        except Exception as e:
            query_time = time.time() - query_start_time
            print(f"❌ 图谱查询失败，耗时: {query_time:.2f}秒")
            print(f"错误信息: {e}")
            return False

        # === 4. 对比普通查询 ===
        print("\n🔄 === 4. 对比普通查询 ===")

        try:
            normal_start_time = time.time()

            # 普通查询（不使用图谱）
            normal_response = db.query(
                query="解释人工智能、机器学习和深度学习之间的关系",
                temperature=0.7
            )

            normal_time = time.time() - normal_start_time
            print(f"✅ 普通查询成功！耗时: {normal_time:.2f}秒")
            print(f"📄 普通查询结果:")
            print(normal_response.completion)

            print(f"\n📊 性能对比:")
            print(f"   - 图谱查询耗时: {query_time:.2f}秒")
            print(f"   - 普通查询耗时: {normal_time:.2f}秒")

        except Exception as e:
            print(f"❌ 普通查询失败: {e}")

        db.close()
        print("\n🎉 知识图谱教程完成！")
        return True

    except Exception as e:
        print(f"❌ 教程执行过程中出现严重错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Morphik 知识图谱教程 - 扩展超时版本")
    print("=" * 70)
    print("🎯 本教程专门针对知识图谱功能进行了超时优化")
    print("⚙️  配置的超时时间:")
    print("   - 客户端连接: 600秒（10分钟）")
    print("   - 图谱等待: 600秒（10分钟）")
    print("   - 检查间隔: 10秒")
    print()

    success = run_knowledge_graph_tutorial()

    if success:
        print("\n🎉 教程成功完成！")
        print("💡 知识图谱功能正常工作，您可以在应用中使用它")
        print("📋 如果您在生产环境中使用，建议根据数据量调整超时时间")
    else:
        print("\n⚠️  教程未完全成功")
        print("💡 可能的原因:")
        print("   1. 网络连接问题")
        print("   2. 通义千问API限制")
        print("   3. 服务器资源不足")
        print("   4. 配置问题")
        print("🔧 建议:")
        print("   1. 检查网络连接")
        print("   2. 确认API密钥有效")
        print("   3. 重启服务后重试")
        print("   4. 查看日志文件获取详细错误信息")

if __name__ == "__main__":
    main()
