services:
  postgres:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_DB: morphik
      POSTGRES_USER: morphik
      POSTGRES_PASSWORD: morphik
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U morphik -d morphik"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - morphik-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - morphik-network

  morphik-api:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    ports:
      - "8000:8000"
    environment:
      # 数据库配置
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@postgres:5432/morphik
      - PGPASSWORD=morphik
      
      # Redis 配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      
      # API 配置
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      
      # JWT 配置
      - JWT_SECRET_KEY=your-secure-morphik-jwt-key-change-in-production
      
      # Python 配置
      - PYTHONUNBUFFERED=1
    volumes:
      - ./morphik.toml:/app/morphik.toml:ro
      - ./storage:/app/storage
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - morphik-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  morphik-network:
    driver: bridge
