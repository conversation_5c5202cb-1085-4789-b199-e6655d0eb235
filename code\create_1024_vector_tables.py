#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建1024维向量表脚本
强制创建正确维度的向量表
"""

import asyncio
import asyncpg
import sys

async def create_1024_vector_tables():
    """创建1024维向量表"""
    print("🔧 创建1024维向量表...")
    
    try:
        # 连接到 PostgreSQL
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 删除现有的向量表
        print("🗑️  删除现有向量表...")
        await conn.execute("DROP TABLE IF EXISTS vector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS multivector_embeddings CASCADE;")
        print("✅ 已删除现有向量表")
        
        # 创建新的1024维向量表
        print("🏗️  创建新的1024维向量表...")
        
        # 创建向量嵌入表
        await conn.execute("""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        print("✅ 创建 vector_embeddings 表成功")
        
        # 创建多向量嵌入表
        await conn.execute("""
            CREATE TABLE multivector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        print("✅ 创建 multivector_embeddings 表成功")
        
        # 创建索引
        print("📊 创建向量索引...")
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_document_id 
            ON vector_embeddings(document_id);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_embedding 
            ON vector_embeddings USING ivfflat (embedding vector_cosine_ops) 
            WITH (lists = 100);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_document_id 
            ON multivector_embeddings(document_id);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_embedding 
            ON multivector_embeddings USING ivfflat (embedding vector_cosine_ops) 
            WITH (lists = 100);
        """)
        
        print("✅ 已创建所有索引")
        
        # 验证表结构
        print("🔍 验证表结构...")
        result = await conn.fetchrow("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'vector_embeddings' 
            AND column_name = 'embedding';
        """)
        
        if result:
            print(f"✅ vector_embeddings.embedding 列类型: {result['data_type']}")
        
        await conn.close()
        print("🎉 1024维向量表创建完成！")
        
    except Exception as e:
        print(f"❌ 创建向量表时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(create_1024_vector_tables())
