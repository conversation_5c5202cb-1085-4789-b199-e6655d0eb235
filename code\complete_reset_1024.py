#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完全重置向量存储为1024维
删除所有相关表和元数据，重新创建
"""

import asyncio
import asyncpg
import sys

async def complete_reset_to_1024():
    """完全重置为1024维"""
    print("🔧 完全重置向量存储为1024维...")
    
    try:
        # 连接到数据库
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 1. 删除所有向量相关表
        print("🗑️  删除所有向量相关表...")
        tables_to_drop = [
            "vector_embeddings",
            "multivector_embeddings", 
            "vector_store_metadata",
            "documents",
            "chunks"
        ]
        
        for table in tables_to_drop:
            try:
                await conn.execute(f"DROP TABLE IF EXISTS {table} CASCADE;")
                print(f"   ✅ 删除表: {table}")
            except Exception as e:
                print(f"   ⚠️  删除表 {table} 失败: {e}")
        
        # 2. 重新创建元数据表
        print("🏗️  重新创建元数据表...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS vector_store_metadata (
                key VARCHAR PRIMARY KEY,
                value VARCHAR NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # 3. 创建1024维向量表
        print("🏗️  创建1024维向量表...")
        
        await conn.execute("""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        await conn.execute("""
            CREATE TABLE multivector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        print("✅ 1024维向量表创建成功")
        
        # 4. 创建索引
        print("📊 创建索引...")
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_document_id 
            ON vector_embeddings(document_id);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_document_id 
            ON multivector_embeddings(document_id);
        """)
        
        print("✅ 索引创建成功")
        
        # 5. 设置元数据
        print("📝 设置元数据...")
        await conn.execute("""
            INSERT INTO vector_store_metadata (key, value) 
            VALUES ('dimensions', '1024')
            ON CONFLICT (key) DO UPDATE SET value = '1024';
        """)
        
        await conn.execute("""
            INSERT INTO vector_store_metadata (key, value) 
            VALUES ('initialized', 'true')
            ON CONFLICT (key) DO UPDATE SET value = 'true';
        """)
        
        print("✅ 元数据设置成功")
        
        # 6. 验证创建结果
        print("🔍 验证创建结果...")
        
        # 检查表是否存在
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('vector_embeddings', 'multivector_embeddings', 'vector_store_metadata')
            ORDER BY table_name;
        """)
        
        print(f"   📊 创建的表: {[t['table_name'] for t in tables]}")
        
        # 检查向量维度
        vector_info = await conn.fetchrow("""
            SELECT atttypmod 
            FROM pg_attribute 
            WHERE attrelid = 'vector_embeddings'::regclass 
            AND attname = 'embedding';
        """)
        
        print(f"   📏 向量维度: {vector_info['atttypmod'] if vector_info else 'None'}")
        
        # 检查元数据
        metadata = await conn.fetch("""
            SELECT key, value 
            FROM vector_store_metadata 
            ORDER BY key;
        """)
        
        print(f"   📝 元数据: {dict((m['key'], m['value']) for m in metadata)}")
        
        await conn.close()
        
        print("\n🎉 完全重置为1024维成功！")
        return True
        
    except Exception as e:
        print(f"❌ 重置过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("Morphik 向量存储完全重置工具")
    print("=" * 50)
    print("⚠️  警告: 此操作将删除所有现有的向量数据！")
    print("💡 此工具将完全重置向量存储为1024维")
    print()
    
    success = await complete_reset_to_1024()
    
    if success:
        print("\n✅ 重置成功！")
        print("📋 接下来的步骤:")
        print("1. 重启 Morphik 服务")
        print("2. 测试在线模型功能")
        print()
        print("命令:")
        print("  cd .. && ./manage_services.sh restart")
        print("  cd code && python 1.py")
    else:
        print("\n❌ 重置失败")
        print("请检查数据库连接和权限")

if __name__ == "__main__":
    asyncio.run(main())
