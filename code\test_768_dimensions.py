#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试768维向量配置
验证通义千问768维与本地模型的兼容性
"""

import requests
import json

def test_qwen_768_dimensions():
    """测试通义千问768维向量"""
    print("🧪 测试通义千问768维向量配置...")
    
    url = 'https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings'
    headers = {
        'Authorization': 'Bearer sk-a281ffc3039c497f96ba5b62ce6baa3f',
        'Content-Type': 'application/json'
    }
    data = {
        'model': 'text-embedding-v3',
        'input': '这是一个测试768维向量的文本',
        'dimensions': 768,  # 指定768维
        'encoding_format': 'float'
    }
    
    try:
        print("📡 发送API请求...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            embedding = result['data'][0]['embedding']
            dimensions = len(embedding)
            
            print(f"✅ API调用成功")
            print(f"📊 返回向量维度: {dimensions}")
            print(f"🎯 期望维度: 768")
            
            if dimensions == 768:
                print("🎉 维度匹配成功！通义千问768维配置正确")
                return True
            else:
                print(f"❌ 维度不匹配！期望768，实际{dimensions}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_local_model_dimensions():
    """测试本地模型维度"""
    print("\n🧪 测试本地模型维度...")
    
    try:
        # 测试本地嵌入模型
        url = 'http://localhost:11434/api/embeddings'
        data = {
            'model': 'nomic-embed-text:latest',
            'prompt': '这是一个测试本地模型维度的文本'
        }
        
        print("📡 发送本地API请求...")
        response = requests.post(url, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            embedding = result['embedding']
            dimensions = len(embedding)
            
            print(f"✅ 本地模型调用成功")
            print(f"📊 本地模型向量维度: {dimensions}")
            
            return dimensions
        else:
            print(f"❌ 本地模型调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 本地模型测试失败: {e}")
        return None

if __name__ == "__main__":
    print("🔍 向量维度兼容性测试")
    print("=" * 50)
    
    # 测试通义千问768维
    qwen_success = test_qwen_768_dimensions()
    
    # 测试本地模型维度
    local_dimensions = test_local_model_dimensions()
    
    # 总结
    print("\n📋 测试总结:")
    print("=" * 30)
    
    if qwen_success:
        print("✅ 通义千问768维配置: 成功")
    else:
        print("❌ 通义千问768维配置: 失败")
    
    if local_dimensions:
        print(f"✅ 本地模型维度: {local_dimensions}")
        if local_dimensions == 768:
            print("🎉 维度完全匹配！可以无缝切换")
        else:
            print("⚠️  维度不匹配，需要调整配置")
    else:
        print("❌ 本地模型维度: 获取失败")
    
    if qwen_success and local_dimensions == 768:
        print("\n🎉 恭喜！在线和本地模型维度完全兼容")
        print("💡 现在可以安全地在两种模型间切换")
    else:
        print("\n⚠️  需要进一步调整配置以确保兼容性")
