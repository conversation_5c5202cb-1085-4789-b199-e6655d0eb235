#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
向量维度管理器
用于 manage_services.sh 调用，处理向量表维度管理
"""

import asyncio
import asyncpg
import sys
import os

async def ensure_vector_table_dimensions(target_dimensions):
    """确保向量表具有正确的维度"""
    print(f"🔧 确保向量表为 {target_dimensions} 维...")

    try:
        # 连接到数据库
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")

        # 检查当前维度 - 使用更可靠的方法
        try:
            # 方法1: 检查表结构中的向量维度
            vector_info = await conn.fetchrow("""
                SELECT atttypmod
                FROM pg_attribute
                WHERE attrelid = 'vector_embeddings'::regclass
                AND attname = 'embedding';
            """)

            # 方法2: 通过表定义检查维度
            table_def = await conn.fetchrow("""
                SELECT column_name, data_type, character_maximum_length,
                       numeric_precision, numeric_scale, udt_name
                FROM information_schema.columns
                WHERE table_name = 'vector_embeddings'
                AND column_name = 'embedding';
            """)

            current_dimensions = vector_info['atttypmod'] if vector_info else None

            print(f"🔍 检查结果:")
            print(f"   - atttypmod: {current_dimensions}")
            print(f"   - 表定义: {table_def}")

            # 强制重新创建表，因为之前的检查可能不准确
            print(f"🔄 强制重新创建向量表以确保维度正确")

        except Exception as e:
            print(f"⚠️  无法检查当前维度，将重新创建表: {e}")

        # 删除现有向量表
        print("🗑️  删除现有向量表...")
        await conn.execute("DROP TABLE IF EXISTS vector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS multivector_embeddings CASCADE;")

        # 创建新的向量表
        print(f"🏗️  创建 {target_dimensions} 维向量表...")

        await conn.execute(f"""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector({target_dimensions}) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        await conn.execute(f"""
            CREATE TABLE multivector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector({target_dimensions}) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # 创建索引
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_document_id
            ON vector_embeddings(document_id);
        """)

        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_document_id
            ON multivector_embeddings(document_id);
        """)

        # 更新元数据
        await conn.execute(f"""
            INSERT INTO vector_store_metadata (key, value)
            VALUES ('dimensions', '{target_dimensions}')
            ON CONFLICT (key) DO UPDATE SET value = '{target_dimensions}';
        """)

        print(f"✅ {target_dimensions} 维向量表创建成功")

        await conn.close()
        return True

    except Exception as e:
        print(f"❌ 向量表操作失败: {e}")
        return False

async def check_vector_table_dimensions():
    """检查当前向量表维度"""
    try:
        # 连接到数据库
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )

        # 检查当前维度
        try:
            vector_info = await conn.fetchrow("""
                SELECT atttypmod
                FROM pg_attribute
                WHERE attrelid = 'vector_embeddings'::regclass
                AND attname = 'embedding';
            """)

            current_dimensions = vector_info['atttypmod'] if vector_info else None
            await conn.close()
            return current_dimensions

        except Exception:
            await conn.close()
            return None

    except Exception:
        return None

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python vector_dimension_manager.py [ensure|check] [dimensions]")
        print("  ensure <dimensions> - 确保向量表为指定维度")
        print("  check               - 检查当前向量表维度")
        sys.exit(1)

    command = sys.argv[1]

    if command == "ensure":
        if len(sys.argv) != 3:
            print("错误: ensure 命令需要指定维度")
            print("用法: python vector_dimension_manager.py ensure <dimensions>")
            sys.exit(1)

        try:
            target_dimensions = int(sys.argv[2])
        except ValueError:
            print("错误: 维度必须是数字")
            sys.exit(1)

        success = await ensure_vector_table_dimensions(target_dimensions)
        sys.exit(0 if success else 1)

    elif command == "check":
        dimensions = await check_vector_table_dimensions()
        if dimensions is not None:
            print(f"当前向量表维度: {dimensions}")
            sys.exit(0)
        else:
            print("无法检查向量表维度")
            sys.exit(1)
    else:
        print(f"错误: 未知命令 '{command}'")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
