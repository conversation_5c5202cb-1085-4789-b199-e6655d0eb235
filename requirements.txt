accelerate==1.6.0
aiofiles==24.1.0
aiohttp==3.9.5
aiosignal==1.3.1
alembic==1.13.1
altair==5.5.0
annotated-types==0.6.0
anthropic==0.42.0
antlr4-python3-runtime==4.9.3
anyio==4.3.0
appnope==0.1.4
arq==0.25.0
asgiref==3.8.1
assemblyai==0.36.0
astor==0.8.1
asttokens==2.4.1
asyncpg==0.29.0
attrs==23.2.0
babel==2.17.0
backoff==2.2.1
bcrypt==4.0.1
beautifulsoup4==4.12.3
black==24.10.0
blinker==1.9.0
boto3==1.36.20
boto3-stubs==1.34.150
botocore==1.36.20
botocore-stubs==1.34.150
build==1.2.2.post1
cachetools==5.3.3
cbor==1.0.0
certifi==2024.2.2
cffi==1.17.0
cfgv==3.4.0
chardet==5.2.0
charset-normalizer==3.4.1
chinese_calendar==1.10.0
click==8.1.7
coloredlogs==15.0.1
colorlog==6.9.0
colpali_engine @ git+https://github.com/illuin-tech/colpali@80fb72c9b827ecdb5687a3a8197077d0d01791b3
comm==0.2.2
contourpy==1.2.1
courlan==1.3.2
cryptography==43.0.0
cssselect==1.3.0
cssutils==2.11.1
cycler==0.12.1
dataclasses-json==0.6.7
datasets==2.19.0
dateparser==1.2.1
debugpy==1.8.5
decorator==5.1.1
deepdiff==7.0.1
defusedxml==0.7.1
Deprecated==1.2.14
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
dnspython==2.6.1
docstring_parser==0.16
docutils==0.21.2
ecdsa==0.19.0
effdet==0.4.1
einops==0.8.1
email_validator==2.1.1
emoji==2.12.1
et-xmlfile==1.1.0
executing==2.0.1
faiss-cpu==1.11.0
fastapi==0.111.0
fastapi-cli==0.0.2
ffmpeg-python==0.2.0
filelock==3.15.4
filetype==1.2.0
fireworks-ai==0.15.12
FlagEmbedding==1.3.4
flake8==7.0.0
flatbuffers==24.3.25
fonttools==4.53.1
frozenlist==1.4.1
fsspec==2024.3.1
ftfy==6.3.1
future==1.0.0
gitdb==4.0.12
GitPython==3.1.44
google-ai-generativelanguage==0.6.15
google-api-core==2.19.1
google-api-python-client==2.161.0
google-auth==2.29.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
google-cloud-vision==3.7.4
google-genai==1.2.0
google-generativeai==0.8.4
googleapis-common-protos==1.63.2
GPUtil==1.4.0
greenlet==3.1.1
grpcio==1.65.4
grpcio-status==1.65.4
h11==0.14.0
h2==4.2.0
hiredis==3.1.0
hpack==4.1.0
html2text==2024.2.26
htmldate==1.9.3
httpcore==1.0.5
httplib2==0.22.0
httptools==0.6.1
httpx==0.28.1
httpx-sse==0.4.0
httpx-ws==0.7.1
huggingface-hub==0.30.2
humanfriendly==10.0
hyperframe==6.1.0
identify==2.6.3
idna==3.7
ijson==3.3.0
imageio==2.37.0
imagesize==1.4.1
importlib_metadata==8.5.0
iniconfig==2.0.0
inquirerpy==0.3.4
inscriptis==2.5.0
instructor==1.7.9
iopath==0.1.10
ipykernel==6.29.5
ipython==8.26.0
ir_datasets==0.5.9
isort==6.0.1
itsdangerous==2.2.0
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jedi==0.19.1
Jinja2==3.1.4
jiter==0.8.2
jmespath==1.0.1
joblib==1.4.2
jsonpatch==1.33
jsonpath-python==1.0.6
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.2
jupyter_core==5.7.2
jusText==3.0.1
keyring==25.5.0
kiwisolver==1.4.5
langchain==0.2.17
langchain-community==0.2.17
langchain-core==0.2.43
langchain-openai==0.1.25
langchain-text-splitters==0.2.4
langdetect==1.0.9
langsmith==0.1.147
lap==0.5.12
layoutparser==0.3.4
lazy_loader==0.4
litellm==1.67.5
llama_cpp_python==0.3.5
llvmlite==0.43.0
lmnr==0.4.60
lxml==5.3.1
lxml_html_clean==0.4.1
lz4==4.3.3
MainContentExtractor==0.0.4
Mako==1.3.9
Markdown==3.6
markdown-it-py==3.0.0
markdownify==0.14.1
MarkupSafe==2.1.5
marshmallow==3.21.3
matplotlib==3.9.2
matplotlib-inline==0.1.7
mccabe==0.7.0
mdurl==0.1.2
monotonic==1.6
more-itertools==10.5.0
motor==3.4.0
mpmath==1.3.0
multidict==6.0.5
multiprocess==0.70.16
mypy==1.15.0
mypy-boto3-s3==1.34.138
mypy-extensions==1.0.0
narwhals==1.26.0
nest-asyncio==1.6.0
networkx==3.3
nh3==0.2.20
nltk==3.8.1
nodeenv==1.9.1
numpy==1.26.4
oauthlib==3.2.2
olefile==0.47
ollama==0.4.7
omegaconf==2.3.0
onnx==1.16.2
onnxruntime==1.18.1
openai==1.76.2
opencv-contrib-python==*********
opencv-python==*********
openpyxl==3.1.5
opentelemetry-api==1.29.0
opentelemetry-exporter-otlp==1.29.0
opentelemetry-exporter-otlp-proto-common==1.29.0
opentelemetry-exporter-otlp-proto-grpc==1.29.0
opentelemetry-exporter-otlp-proto-http==1.29.0
opentelemetry-instrumentation==0.50b0
opentelemetry-instrumentation-asgi==0.50b0
opentelemetry-instrumentation-fastapi==0.50b0
opentelemetry-instrumentation-langchain==0.38.5
opentelemetry-instrumentation-requests==0.50b0
opentelemetry-instrumentation-sqlalchemy==0.50b0
opentelemetry-instrumentation-threading==0.50b0
opentelemetry-instrumentation-urllib3==0.50b0
opentelemetry-proto==1.29.0
opentelemetry-sdk==1.29.0
opentelemetry-semantic-conventions==0.50b0
opentelemetry-semantic-conventions-ai==0.4.2
opentelemetry-util-http==0.50b0
opt-einsum==3.3.0
ordered-set==4.1.0
orjson==3.10.3
packaging==24.0
paddlepaddle==3.0.0rc0
paddlex==3.0.0rc1
pandas==1.5.3
parso==0.8.4
passlib==1.7.4
pathspec==0.12.1
pdf2image==1.17.0
pdfminer.six==20231228
pdfplumber==0.11.3
peft==0.14.0
pexpect==4.9.0
pfzy==0.3.4
pgvector==0.3.6
pikepdf==9.1.1
pillow==10.4.0
pillow_heif==0.18.0
pkginfo==1.12.0
platformdirs==4.2.2
playwright==1.50.0
pluggy==1.5.0
portalocker==2.10.1
posthog==3.13.0
pre_commit==4.0.1
premailer==3.10.0
prettytable==3.16.0
prompt_toolkit==3.0.47
proto-plus==1.24.0
protobuf==5.27.3
psutil==6.0.0
psycopg==3.1.18
psycopg-binary==3.1.18
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyarrow==17.0.0
pyarrow-hotfix==0.6
pyasn1==0.6.0
pyasn1_modules==0.4.0
pyclipper==1.3.0.post6
pycocotools==2.0.8
pycodestyle==2.11.1
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.4.0
pydantic_core==2.27.2
pydeck==0.9.1
pyee==12.1.1
pyflakes==3.2.0
Pygments==2.18.0
PyJWT==2.9.0
pymongo==4.7.1
PyMuPDF==1.25.5
pypandoc==1.13
pyparsing==3.1.2
pypdf==4.3.1
pypdfium2==4.30.0
pyproject-flake8==7.0.0
pyproject_hooks==1.2.0
pyright==1.1.399
pytesseract==0.3.10
pytest==8.2.0
pytest-asyncio==0.24.0
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-iso639==2024.4.27
python-jose==3.3.0
python-magic==0.4.27
python-multipart==0.0.9
python-oxmsg==0.0.1
python-pptx==0.6.23
pytubefix==8.8.5
pytz==2025.1
PyYAML==6.0.2
pyzmq==26.2.0
rank-bm25==0.2.2
rapidfuzz==3.9.5
readme_renderer==44.0
redis==5.2.1
referencing==0.36.2
regex==2024.7.24
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3986==2.0.0
rich==13.7.1
rpds-py==0.22.3
rsa==4.9
ruamel.yaml==0.18.10
ruamel.yaml.clib==0.2.12
ruff==0.11.5
s3transfer==0.11.2
safetensors==0.5.3
scikit-image==0.25.2
scikit-learn==1.6.0
scipy==1.14.0
seaborn==0.13.2
sentence-transformers==3.3.1
sentencepiece==0.2.0
setuptools==75.8.0
shapely==2.1.0
shellingham==1.5.4
six==1.16.0
smmap==5.0.2
sniffio==1.3.1
soundfile==0.13.1
soupsieve==2.5
SQLAlchemy==2.0.38
stack-data==0.6.3
starlette==0.37.2
streamlit==1.42.0
sympy==1.13.1
tabulate==0.9.0
tenacity==8.5.0
threadpoolctl==3.5.0
tifffile==2025.3.30
tiktoken==0.7.0
timm==1.0.8
tld==0.13
tokenizers==0.21.1
toml==0.10.2
tomli==2.2.1
torch==2.6.0
torchaudio==2.6.0
torchvision==0.21.0
tornado==6.4.1
tqdm==4.66.4
trafilatura==2.0.0
traitlets==5.14.3
transformers==4.51.3
trec-car-tools==2.6
twine==6.0.1
typer==0.12.3
types-awscrt==0.21.2
types-s3transfer==0.10.1
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
tzlocal==5.3
ujson==5.9.0
ultralytics==8.3.55
ultralytics-thop==2.0.13
unlzw3==0.2.3
unstructured==0.15.0
unstructured-client==0.24.1
unstructured-inference==0.7.36
unstructured.pytesseract==0.3.12
uritemplate==4.1.1
urllib3==2.2.1
uvicorn==0.29.0
uvloop==0.19.0
virtualenv==20.28.0
warc3-wet==0.2.5
warc3-wet-clueweb09==0.2.5
watchfiles==0.21.0
wcwidth==0.2.13
websockets==14.2
wrapt==1.16.0
wsproto==1.2.0
xlrd==2.0.1
XlsxWriter==3.2.0
xxhash==3.4.1
yarl==1.9.4
zipp==3.21.0
zlib-state==0.1.9
zstandard==0.23.0
