#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Morphik Python SDK 完整教程 - 第三部分：大语言模型配置与实际应用
===========================================================

本教程将循序渐进地介绍 Morphik Python SDK 的主要功能，包含详细的中文注释。
教程分为多个部分，从基础功能开始，逐步深入到高级特性。

第三部分内容：
1. 大语言模型配置
   - 在线大模型配置（OpenAI、Anthropic）
   - 本地 Ollama 模型配置
   - 自定义模型服务
   - 动态切换模型
2. 高级查询与提示词覆盖
3. 实际应用场景：智能文档分析系统

作者：AI助手
日期：2024年
"""

import os
import time
import json
from typing import Dict, List, Any, Optional, Type, Union
from pydantic import BaseModel, Field

# 导入 Morphik 相关模块
from morphik import Morphik
from morphik.models import (
    LLMConfig,
    OpenAIConfig,
    AnthropicConfig,
    OllamaConfig,
    CustomLLMConfig,
    QueryPromptOverride,
    QueryPromptOverrides,
    RetrievalPromptOverride
)

# =====================================================================
# 1. 大语言模型配置
# =====================================================================

def llm_configuration_demo():
    """
    演示如何配置和使用不同的大语言模型
    
    Morphik 支持多种大语言模型配置方式，包括：
    - 在线 API 服务（如 OpenAI、Anthropic）
    - 本地部署的模型（如 Ollama）
    - 自定义模型服务
    """
    print("\n===== 1. 大语言模型配置 =====")
    
    # ----- 1.1 在线大模型配置 -----
    print("\n--- 1.1 在线大模型配置 ---")
    
    # OpenAI 模型配置
    print("OpenAI 模型配置示例:")
    print("""
    # 设置 OpenAI API 密钥
    os.environ["OPENAI_API_KEY"] = "your-openai-api-key"
    
    # 创建 OpenAI 配置
    openai_config = OpenAIConfig(
        model="gpt-4",  # 模型名称：gpt-4, gpt-4-turbo, gpt-3.5-turbo 等
        temperature=0.7,  # 生成的随机性：0.0-1.0，值越低结果越确定
        max_tokens=500,  # 最大生成令牌数
        top_p=0.95,  # 核采样参数：控制输出的多样性
        frequency_penalty=0.0,  # 频率惩罚：降低重复内容的可能性
        presence_penalty=0.0  # 存在惩罚：增加新主题出现的可能性
    )
    
    # 初始化 Morphik 客户端并配置 LLM
    db = Morphik(
        llm_config=LLMConfig(
            provider="openai",  # 提供商名称
            config=openai_config  # 提供商特定配置
        )
    )
    """)
    
    # Anthropic 模型配置
    print("\nAnthropic 模型配置示例:")
    print("""
    # 设置 Anthropic API 密钥
    os.environ["ANTHROPIC_API_KEY"] = "your-anthropic-api-key"
    
    # 创建 Anthropic 配置
    anthropic_config = AnthropicConfig(
        model="claude-3-opus-20240229",  # 模型名称：claude-3-opus, claude-3-sonnet, claude-3-haiku 等
        temperature=0.5,  # 生成的随机性
        max_tokens=1000,  # 最大生成令牌数
        top_p=0.9  # 核采样参数
    )
    
    # 初始化 Morphik 客户端并配置 LLM
    db = Morphik(
        llm_config=LLMConfig(
            provider="anthropic",
            config=anthropic_config
        )
    )
    """)
    
    # ----- 1.2 本地 Ollama 模型配置 -----
    print("\n--- 1.2 本地 Ollama 模型配置 ---")
    
    print("Ollama 是一个强大的本地大模型运行工具，支持多种开源模型")
    print("使用前需要安装并启动 Ollama 服务：")
    print("""
    # 安装 Ollama（根据操作系统选择适当的安装方法）
    # 详见：https://ollama.ai/download
    
    # 启动 Ollama 服务
    ollama serve
    
    # 拉取所需模型（例如 Llama 2）
    ollama pull llama2
    """)
    
    print("\nOllama 模型配置示例:")
    print("""
    # 创建 Ollama 配置
    ollama_config = OllamaConfig(
        model="llama2",  # 模型名称（确保已在 Ollama 中拉取）
        temperature=0.7,  # 生成的随机性
        max_tokens=500,  # 最大生成令牌数
        top_p=0.9,  # 核采样参数
        host="http://localhost:11434"  # Ollama 服务地址（默认端口为 11434）
    )
    
    # 初始化 Morphik 客户端并配置 LLM
    db = Morphik(
        llm_config=LLMConfig(
            provider="ollama",
            config=ollama_config
        )
    )
    """)
    
    print("\n使用量化版 Ollama 模型（更小，速度更快）:")
    print("""
    # 创建量化版 Ollama 配置
    ollama_quantized_config = OllamaConfig(
        model="llama2:7b-q4_0",  # 量化模型名称，q4_0 表示 4 位量化
        temperature=0.7,
        max_tokens=500,
        host="http://localhost:11434"
    )
    
    # 初始化 Morphik 客户端并配置 LLM
    db = Morphik(
        llm_config=LLMConfig(
            provider="ollama",
            config=ollama_quantized_config
        )
    )
    """)
    
    # ----- 1.3 自定义模型服务 -----
    print("\n--- 1.3 自定义模型服务 ---")
    
    print("自定义模型服务配置示例:")
    print("""
    # 创建自定义 LLM 配置
    custom_config = CustomLLMConfig(
        endpoint="http://your-custom-llm-server:8080/generate",  # 自定义端点
        headers={
            "Authorization": "Bearer your-api-key",
            "Content-Type": "application/json"
        },
        # 请求参数映射（将 Morphik 参数映射到服务参数）
        request_mapping={
            "prompt": "input",  # 将 Morphik 的 prompt 映射到服务的 input 字段
            "temperature": "temp",  # 参数名称映射
            "max_tokens": "max_length"
        },
        # 响应映射（将服务响应映射到 Morphik 格式）
        response_mapping={
            "completion": "generated_text",  # 将服务的 generated_text 映射到 Morphik 的 completion
            "usage.total_tokens": "usage.total"
        }
    )
    
    # 初始化 Morphik 客户端并配置自定义 LLM
    db = Morphik(
        llm_config=LLMConfig(
            provider="custom",
            config=custom_config
        )
    )
    """)
    
    # ----- 1.4 动态切换模型 -----
    print("\n--- 1.4 动态切换模型 ---")
    
    print("动态切换模型示例:")
    print("""
    # 初始化默认客户端（使用 OpenAI）
    db = Morphik(
        llm_config=LLMConfig(
            provider="openai",
            config=OpenAIConfig(model="gpt-4")
        )
    )
    
    # 使用默认模型进行查询
    standard_response = db.query(
        "总结人工智能的主要应用领域"
    )
    
    # 对于复杂分析，使用 Claude 模型
    complex_response = db.query(
        "分析量子计算对密码学的潜在影响，包括优势和风险",
        llm_config=LLMConfig(  # 在查询时覆盖默认配置
            provider="anthropic",
            config=AnthropicConfig(
                model="claude-3-opus-20240229",
                temperature=0.3  # 降低温度以获得更确定的分析
            )
        )
    )
    
    # 对于敏感数据查询，切换到本地 Ollama 模型
    sensitive_response = db.query(
        "分析公司内部财务数据趋势",
        llm_config=LLMConfig(
            provider="ollama",
            config=OllamaConfig(
                model="llama2",
                temperature=0.2
            )
        )
    )
    """)
    
    print("\n模型选择最佳实践:")
    print("  - 对于敏感数据处理，优先使用本地模型如 Ollama")
    print("  - 对于需要高质量结果的场景，考虑使用 OpenAI 或 Anthropic 的高级模型")
    print("  - 根据任务复杂度选择适当的模型大小和参数")
    print("  - 在生产环境中，确保妥善管理和保护 API 密钥")
    print("  - 对于高流量应用，考虑实现模型回退和负载均衡策略")

# =====================================================================
# 2. 高级查询与提示词覆盖
# =====================================================================

def advanced_query_demo(db: Morphik):
    """
    演示高级查询和提示词覆盖功能
    
    Morphik 允许自定义查询过程中使用的提示词模板，
    以控制检索和生成的行为，获得更专业、更符合特定需求的结果。
    """
    print("\n===== 2. 高级查询与提示词覆盖 =====")
    
    # ----- 2.1 准备示例文档 -----
    print("\n--- 2.1 准备示例文档 ---")
    
    # 摄取一些示例文档用于高级查询
    medical_doc = db.ingest_text(
        content="""
        高血压是一种常见的慢性疾病，特征是血压持续升高。
        正常血压通常定义为收缩压低于120 mmHg且舒张压低于80 mmHg。
        高血压的诊断标准是收缩压≥140 mmHg和/或舒张压≥90 mmHg。
        
        高血压的风险因素包括：
        - 年龄增长
        - 家族史
        - 肥胖
        - 高盐饮食
        - 缺乏运动
        - 过度饮酒
        - 吸烟
        - 慢性压力
        
        高血压的治疗方法包括生活方式改变和药物治疗。
        生活方式改变包括减少盐摄入、增加运动、减轻体重、限制酒精摄入和戒烟。
        常用的降压药物包括利尿剂、ACE抑制剂、ARB、钙通道阻滞剂和β受体阻滞剂。
        """,
        filename="hypertension.txt",
        metadata={"领域": "医学", "主题": "高血压"}
    )
    
    print(f"已摄取医学文档: {medical_doc.external_id}")
    
    # 等待文档索引完成
    print("等待文档索引完成...")
    time.sleep(3)
    
    # ----- 2.2 自定义查询提示词 -----
    print("\n--- 2.2 自定义查询提示词 ---")
    
    # 使用自定义提示词模板
    custom_query_response = db.query(
        query="高血压的主要风险因素是什么？",
        prompt_overrides=QueryPromptOverrides(
            query=QueryPromptOverride(
                prompt_template="作为医学专家，请以专业医学术语回答以下问题，包括详细解释和相关研究依据：{question}"
            )
        )
    )
    
    print("使用自定义查询提示词的结果:")
    print(custom_query_response.completion)
    
    # ----- 2.3 自定义检索提示词 -----
    print("\n--- 2.3 自定义检索提示词 ---")
    
    # 自定义检索提示词
    retrieval_override_response = db.query(
        query="高血压的治疗方法",
        prompt_overrides=QueryPromptOverrides(
            retrieval=RetrievalPromptOverride(
                prompt_template="查找与以下医学问题相关的具体治疗方案和药物信息：{question}"
            )
        )
    )
    
    print("使用自定义检索提示词的结果:")
    print(retrieval_override_response.completion)
    
    # ----- 2.4 完全自定义提示词 -----
    print("\n--- 2.4 完全自定义提示词 ---")
    
    # 完全自定义所有提示词阶段
    full_override_response = db.query(
        query="高血压患者的饮食建议",
        prompt_overrides=QueryPromptOverrides(
            # 自定义检索阶段
            retrieval=RetrievalPromptOverride(
                prompt_template="查找与以下医学问题相关的详细饮食指南和建议：{question}"
            ),
            # 自定义查询阶段
            query=QueryPromptOverride(
                prompt_template="作为营养学专家，请针对以下问题提供详细的饮食建议，包括具体食物推荐和避免清单：{question}"
            ),
            # 自定义重排序阶段
            rerank=RetrievalPromptOverride(
                prompt_template="选择最相关的医学和营养信息来回答：{question}"
            )
        )
    )
    
    print("使用完全自定义提示词的结果:")
    print(full_override_response.completion)

# =====================================================================
# 3. 实际应用场景：智能文档分析系统
# =====================================================================

def intelligent_document_analysis_system(db: Morphik):
    """
    演示一个完整的实际应用场景：智能文档分析系统
    
    这个示例展示了如何结合 Morphik 的多种功能，构建一个能够：
    1. 自动提取文档元数据
    2. 构建知识图谱
    3. 进行多角度分析
    4. 生成结构化报告
    的智能文档分析系统。
    """
    print("\n===== 3. 实际应用场景：智能文档分析系统 =====")
    
    # ----- 3.1 系统初始化 -----
    print("\n--- 3.1 系统初始化 ---")
    
    # 创建专用文件夹
    analysis_folder = db.create_folder(
        name="document_analysis_system",
        description="智能文档分析系统"
    )
    print(f"已创建分析系统文件夹: {analysis_folder.name}")
    
    # 定义文档元数据模型
    class DocumentMetadata(BaseModel):
        """文档元数据模型"""
        标题: str
        作者: Optional[str] = None
        日期: Optional[str] = None
        主题: List[str]
        关键概念: Optional[List[str]] = None
        
    # 定义分析报告模型
    class AnalysisReport(BaseModel):
        """分析报告模型"""
        主要发现: str
        关键主题: List[str]
        重要关系: List[str]
        建议: List[str]
        置信度: float = Field(ge=0.0, le=1.0)  # 0.0 到 1.0 之间的值
    
    # ----- 3.2 文档摄取与处理 -----
    print("\n--- 3.2 文档摄取与处理 ---")
    
    # 摄取示例研究报告
    report_content = """
    # 可再生能源发展趋势研究报告
    
    作者: 李明 | 日期: 2024-04-20
    
    ## 摘要
    
    本报告分析了全球可再生能源的发展趋势，重点关注太阳能、风能和氢能技术的进步和市场前景。
    研究表明，可再生能源正在经历快速增长，成本持续下降，有望在未来十年内在多个地区实现与传统能源的平价。
    
    ## 太阳能发展
    
    太阳能光伏技术效率不断提高，从2010年的平均15%提升到2023年的22%。同时，成本下降了约85%。
    主要技术突破包括钙钛矿太阳能电池、双面光伏组件和集成储能系统。
    中国、美国和印度是太阳能装机容量增长最快的国家。
    
    ## 风能发展
    
    风力发电技术也取得显著进步，特别是海上风电领域。
    单个风机容量从2010年的平均3MW增加到2023年的8MW以上，大型海上风机甚至达到15MW。
    欧洲是海上风电的领导者，而中国在陆上风电装机容量方面居全球首位。
    
    ## 氢能前景
    
    绿色氢能被视为难以电气化行业（如钢铁、化工、长途运输）的关键脱碳技术。
    电解槽技术进步和可再生能源成本下降正推动绿氢成本降低。
    预计到2030年，多个地区的绿氢成本将降至每公斤2美元以下，实现商业可行性。
    
    ## 政策与投资
    
    全球政策支持对可再生能源发展至关重要，如欧盟的"绿色协议"、美国的"通胀削减法案"和中国的"双碳"目标。
    2023年，全球可再生能源投资达到5000亿美元，预计到2030年将增至每年1万亿美元。
    
    ## 结论与建议
    
    可再生能源正处于从补充能源向主导能源转变的关键阶段。
    建议政策制定者加强政策稳定性，投资者关注技术创新领域，企业加速能源转型战略。
    能源存储、智能电网和跨区域电力互联将是支持高比例可再生能源整合的关键。
    
    关键词: 可再生能源, 太阳能, 风能, 氢能, 能源转型, 碳中和
    """
    
    # 使用元数据提取规则摄取文档
    doc = analysis_folder.ingest_text(
        content=report_content,
        filename="renewable_energy_report.md",
        metadata={"类型": "研究报告", "行业": "能源"},
        rules=[
            # 自动提取元数据
            MetadataExtractionRule(schema=DocumentMetadata)
        ]
    )
    
    print(f"已摄取并处理研究报告: {doc.external_id}")
    
    # 获取提取的元数据
    processed_doc = db.get_document(doc.external_id)
    print("\n自动提取的元数据:")
    for key, value in processed_doc.metadata.items():
        if key not in ["类型", "行业"]:  # 只显示自动提取的元数据
            print(f"  - {key}: {value}")
    
    # 等待文档索引完成
    print("等待文档索引完成...")
    time.sleep(3)
    
    # ----- 3.3 知识图谱构建 -----
    print("\n--- 3.3 知识图谱构建 ---")
    
    # 创建知识图谱
    graph = analysis_folder.create_graph(
        name="energy_knowledge_graph",
        prompt_overrides=GraphPromptOverrides(
            entity_extraction=EntityExtractionPromptOverride(
                examples=[
                    EntityExtractionExample(label="太阳能", type="能源类型"),
                    EntityExtractionExample(label="光伏技术", type="技术"),
                    EntityExtractionExample(label="中国", type="国家"),
                    EntityExtractionExample(label="成本下降", type="趋势")
                ]
            )
        )
    )
    
    print(f"已创建知识图谱: {graph.name}")
    print("等待图谱处理完成...")
    graph = db.wait_for_graph_completion("energy_knowledge_graph")
    
    print(f"图谱处理完成，包含 {len(graph.entities)} 个实体和 {len(graph.relationships)} 个关系")
    
    # ----- 3.4 多角度分析 -----
    print("\n--- 3.4 多角度分析 ---")
    
    # 1. 技术趋势分析
    tech_analysis = analysis_folder.query(
        query="分析报告中提到的可再生能源技术进步和效率提升",
        graph_name="energy_knowledge_graph",
        hop_depth=2
    )
    
    print("\n技术趋势分析:")
    print(tech_analysis.completion)
    
    # 2. 市场前景分析
    market_analysis = analysis_folder.query(
        query="分析全球可再生能源市场的区域差异和投资趋势",
        graph_name="energy_knowledge_graph",
        hop_depth=2
    )
    
    print("\n市场前景分析:")
    print(market_analysis.completion)
    
    # 3. 政策影响分析
    policy_analysis = analysis_folder.query(
        query="评估政策对可再生能源发展的影响",
        graph_name="energy_knowledge_graph",
        hop_depth=2
    )
    
    print("\n政策影响分析:")
    print(policy_analysis.completion)
    
    # ----- 3.5 生成结构化报告 -----
    print("\n--- 3.5 生成结构化报告 ---")
    
    # 生成综合分析报告
    final_report = analysis_folder.query(
        query="基于文档内容，生成一份关于可再生能源发展趋势的综合分析报告",
        schema=AnalysisReport,
        graph_name="energy_knowledge_graph",
        temperature=0.3  # 低温度有助于生成更结构化的输出
    )
    
    print("结构化分析报告:")
    if isinstance(final_report.completion, dict):
        # 将字典转换为格式化输出
        print(f"主要发现:\n{final_report.completion.get('主要发现', 'N/A')}\n")
        
        print("关键主题:")
        for topic in final_report.completion.get('关键主题', []):
            print(f"  - {topic}")
        
        print("\n重要关系:")
        for relation in final_report.completion.get('重要关系', []):
            print(f"  - {relation}")
        
        print("\n建议:")
        for recommendation in final_report.completion.get('建议', []):
            print(f"  - {recommendation}")
        
        print(f"\n置信度: {final_report.completion.get('置信度', 'N/A')}")
        
        # 保存报告为JSON文件（示例代码，注释掉以避免实际写入）
        """
        with open("renewable_energy_analysis_report.json", "w", encoding="utf-8") as f:
            json.dump(final_report.completion, f, ensure_ascii=False, indent=2)
        print("\n报告已保存为JSON文件: renewable_energy_analysis_report.json")
        """
    else:
        # 如果无法生成结构化输出，则显示原始文本
        print(final_report.completion)
    
    # ----- 3.6 系统总结 -----
    print("\n--- 3.6 系统总结 ---")
    
    print("智能文档分析系统演示完成，该系统展示了如何结合 Morphik 的多种功能：")
    print("1. 自动元数据提取：从非结构化文档中提取结构化信息")
    print("2. 知识图谱构建：捕捉文档中的实体和关系")
    print("3. 多角度分析：从不同维度分析文档内容")
    print("4. 结构化报告生成：将分析结果整合为结构化输出")
    print("\n这种系统可以应用于多种场景，如：")
    print("- 研究报告自动分析")
    print("- 市场情报收集与分析")
    print("- 专利文档处理")
    print("- 学术文献综述")
    print("- 政策文件解读")

# =====================================================================
# 主函数
# =====================================================================

def main():
    """主函数：按顺序演示 Morphik 的高级功能和实际应用场景"""
    print("Morphik Python SDK 教程 - 第三部分：大语言模型配置与实际应用")
    print("=" * 60)
    
    try:
        # 1. 大语言模型配置（仅展示，不实际执行）
        llm_configuration_demo()
        
        # 初始化 Morphik 客户端
        db = Morphik(is_local=True)
        
        # 2. 高级查询与提示词覆盖
        advanced_query_demo(db)
        
        # 3. 实际应用场景：智能文档分析系统
        intelligent_document_analysis_system(db)
        
        print("\n教程第三部分完成！")
        print("恭喜！您已经完成了 Morphik Python SDK 的完整教程。")
        print("现在您已经掌握了从基础到高级的所有主要功能，可以开始构建自己的智能应用了。")
        
    except Exception as e:
        print(f"教程执行过程中出错: {e}")
    finally:
        # 确保关闭连接
        if 'db' in locals():
            db.close()
            print("已关闭 Morphik 连接")

if __name__ == "__main__":
    main()
