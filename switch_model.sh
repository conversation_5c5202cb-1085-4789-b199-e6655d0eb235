#!/bin/bash

# Morphik 模型切换脚本
# 用法: ./switch_model.sh [local|online] [restart]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置文件路径
MORPHIK_CONFIG="morphik.toml"
BACKUP_CONFIG="morphik.toml.backup"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Morphik 模型切换工具${NC}"
    echo ""
    echo "用法: $0 [模式] [选项]"
    echo ""
    echo "模式:"
    echo "  local     - 切换到本地 Ollama 模型"
    echo "  online    - 切换到在线通义千问模型"
    echo "  status    - 显示当前模型配置"
    echo ""
    echo "选项:"
    echo "  restart   - 切换后自动重启服务"
    echo ""
    echo "示例:"
    echo "  $0 local          # 切换到本地模型"
    echo "  $0 online restart # 切换到在线模型并重启服务"
    echo "  $0 status         # 查看当前配置"
}

# 备份配置文件
backup_config() {
    if [ ! -f "$BACKUP_CONFIG" ]; then
        echo -e "${YELLOW}创建配置文件备份...${NC}"
        cp "$MORPHIK_CONFIG" "$BACKUP_CONFIG"
    fi
}

# 切换到本地模型
switch_to_local() {
    echo -e "${GREEN}切换到本地 Ollama 模型...${NC}"

    # 更新所有组件的模型配置
    sed -i.tmp '/^\[completion\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^model = "qwen_embedding_online"/model = "ollama_embedding_local"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 1536/dimensions = 768/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[agent\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[document_analysis\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[parser\.vision\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[rules\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[graph\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"

    # 清理临时文件
    rm -f "$MORPHIK_CONFIG.tmp"

    echo -e "${GREEN}✓ 已切换到本地模型配置${NC}"
    echo "  - LLM: ollama_qwen3_4b (Gemma 3 4B)"
    echo "  - Embedding: ollama_embedding_local (Nomic Embed, 768维)"
}

# 切换到在线模型
switch_to_online() {
    echo -e "${GREEN}切换到在线通义千问模型...${NC}"

    # 更新所有组件的模型配置
    sed -i.tmp '/^\[completion\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^model = "ollama_embedding_local"/model = "qwen_embedding_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 768/dimensions = 1536/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[agent\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[document_analysis\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[parser\.vision\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[rules\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[graph\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"

    # 清理临时文件
    rm -f "$MORPHIK_CONFIG.tmp"

    echo -e "${GREEN}✓ 已切换到在线模型配置${NC}"
    echo "  - LLM: qwen_max_online (通义千问 Max)"
    echo "  - Embedding: qwen_embedding_online (Text Embedding V3, 1536维)"
}

# 显示当前配置状态
show_status() {
    echo -e "${BLUE}当前模型配置状态:${NC}"
    echo ""

    # 检查 completion 模型
    completion_model=$(grep -E "^\[completion\]" -A 5 "$MORPHIK_CONFIG" | grep "^model" | cut -d'"' -f2)
    echo "LLM 模型: $completion_model"

    # 检查 embedding 模型
    embedding_model=$(grep -E "^\[embedding\]" -A 5 "$MORPHIK_CONFIG" | grep "^model" | cut -d'"' -f2)
    echo "嵌入模型: $embedding_model"

    echo ""
    if [[ "$completion_model" == *"ollama"* ]]; then
        echo -e "${GREEN}当前使用: 本地模型${NC}"
    elif [[ "$completion_model" == *"qwen"* ]]; then
        echo -e "${GREEN}当前使用: 在线模型${NC}"
    else
        echo -e "${YELLOW}当前使用: 其他模型${NC}"
    fi
}

# 重启服务
restart_service() {
    echo -e "${YELLOW}重启 Morphik 服务...${NC}"

    # 停止服务
    echo "停止服务..."
    ./code/manage_services.sh stop

    # 等待服务完全停止
    sleep 3

    # 启动服务
    echo "启动服务..."
    ./code/manage_services.sh start

    echo -e "${GREEN}✓ 服务重启完成${NC}"
}

# 主逻辑
main() {
    # 检查是否在正确的目录
    if [ ! -f "$MORPHIK_CONFIG" ]; then
        echo -e "${RED}错误: 找不到 $MORPHIK_CONFIG 文件${NC}"
        echo "请在 Morphik 项目根目录运行此脚本"
        exit 1
    fi

    # 备份配置文件
    backup_config

    case "$1" in
        "local")
            switch_to_local
            ;;
        "online")
            switch_to_online
            ;;
        "status")
            show_status
            return 0
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            return 0
            ;;
        *)
            echo -e "${RED}错误: 未知参数 '$1'${NC}"
            show_help
            exit 1
            ;;
    esac

    # 检查是否需要重启服务
    if [ "$2" = "restart" ]; then
        restart_service
    else
        echo ""
        echo -e "${YELLOW}提示: 配置已更新，需要重启服务才能生效${NC}"
        echo "运行以下命令重启服务:"
        echo "  $0 $1 restart"
        echo "或手动重启:"
        echo "  ./code/manage_services.sh stop && ./code/manage_services.sh start"
    fi

    echo ""
    echo -e "${GREEN}模型切换完成！${NC}"
}

# 运行主函数
main "$@"
