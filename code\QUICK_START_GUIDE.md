# Morphik 模型切换快速指南

## 🚀 一键模型切换

### 切换到本地模型 (推荐用于隐私敏感场景)
```bash
cd /path/to/morphik-Demo
python code/smart_model_switch.py local
```

**本地模型特点:**
- ✅ 完全离线，数据隐私安全
- ✅ 无API调用费用
- ✅ 响应速度快
- ⚠️ 模型能力相对有限
- 📊 向量维度: 768维

### 切换到在线模型 (推荐用于高质量输出场景)
```bash
cd /path/to/morphik-Demo  
python code/smart_model_switch.py online
```

**在线模型特点:**
- ✅ 强大的语言理解和生成能力
- ✅ 支持更复杂的推理任务
- ✅ 持续更新优化
- ⚠️ 需要网络连接和API费用
- 📊 向量维度: 1024维

## 🧪 测试模型功能

切换模型后，运行测试脚本验证功能：

```bash
cd code
python 1.py
```

测试内容包括:
- 📝 文档摄取 (文本、规则处理)
- 🔍 向量检索 (相似度搜索)
- 💬 RAG查询 (检索增强生成)
- 📊 结构化输出
- 📁 文档管理

## 📊 服务状态检查

### 检查所有服务状态
```bash
./manage_services.sh status
```

### 检查当前模型配置
```bash
./manage_services.sh model-status
```

### 手动服务管理
```bash
# 启动服务
./manage_services.sh start

# 停止服务  
./manage_services.sh stop

# 重启服务
./manage_services.sh restart
```

## 🔧 故障排除

### 如果遇到维度错误
```bash
# 检查数据库表结构
cd code && python check_table_structure.py

# 手动修复为768维 (本地模型)
cd code && python fix_768_dimensions.py

# 手动修复为1024维 (在线模型)  
cd code && python fix_1024_dimensions.py
```

### 如果服务启动失败
```bash
# 查看详细日志
tail -f logs/morphik_api.log

# 检查端口占用
lsof -i :8000
lsof -i :5433
lsof -i :6379
lsof -i :11434

# 强制清理并重启
./manage_services.sh stop
docker-compose down
./manage_services.sh start
```

### 如果模型切换失败
```bash
# 手动检查配置文件
cat morphik.toml | grep -A 5 "\[completion\]"
cat morphik.toml | grep -A 5 "\[embedding\]"

# 重新运行智能切换
python code/smart_model_switch.py [local|online]
```

## 📈 性能对比

### 本地模型 (Ollama)
- **响应时间**: ~2-5秒
- **吞吐量**: 中等
- **资源消耗**: 本地GPU/CPU
- **成本**: 免费
- **隐私**: 完全本地

### 在线模型 (通义千问)
- **响应时间**: ~3-8秒 (取决于网络)
- **吞吐量**: 高
- **资源消耗**: 无本地消耗
- **成本**: 按调用计费
- **隐私**: 数据上传到云端

## 🎯 使用建议

### 选择本地模型的场景:
- 🔒 处理敏感或机密数据
- 💰 需要控制API成本
- 🌐 网络环境不稳定
- ⚡ 需要快速响应的简单任务

### 选择在线模型的场景:
- 🧠 需要高质量的文本生成
- 📚 复杂的知识问答任务
- 🎨 创意写作和内容创作
- 📊 复杂的数据分析和推理

## 🔄 最佳实践

1. **开发阶段**: 使用本地模型进行快速迭代
2. **测试阶段**: 使用在线模型验证最终效果
3. **生产环境**: 根据具体需求选择合适模型
4. **定期切换**: 对比两种模型的表现差异

## 📞 技术支持

如果遇到问题，请检查:
1. 📋 `logs/morphik_api.log` - API服务日志
2. 📋 `logs/worker.log` - 后台任务日志  
3. 🐳 Docker容器状态
4. 🔌 网络连接 (在线模型)
5. 💾 磁盘空间和内存使用

---

**提示**: 智能切换工具会自动处理所有配置和数据库更改，通常无需手动干预。如有问题，请参考故障排除部分。
