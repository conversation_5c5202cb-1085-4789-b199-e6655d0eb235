#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Morphik 模型对比演示
展示本地模型和在线模型的性能差异
"""

import time
import subprocess
import sys
import os
from morphik import Morphik

def run_model_switch(model_type):
    """切换模型"""
    print(f"🔄 切换到 {model_type} 模型...")
    try:
        result = subprocess.run([
            "python", "../code/smart_model_switch.py", model_type
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ 成功切换到 {model_type} 模型")
            return True
        else:
            print(f"❌ 切换失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 切换过程出错: {e}")
        return False

def test_model_performance(model_name):
    """测试模型性能"""
    print(f"\n🧪 测试 {model_name} 模型性能")
    print("=" * 50)
    
    try:
        # 连接到 Morphik
        db = Morphik(is_local=True, timeout=60)
        
        # 测试文档
        test_content = """
        区块链技术是一种分布式账本技术，具有去中心化、不可篡改、透明度高等特点。
        它最初作为比特币的底层技术被提出，现在已经扩展到金融、供应链、医疗、
        政务等多个领域。区块链通过密码学哈希、数字签名、共识机制等技术手段，
        确保数据的安全性和可信度。智能合约作为区块链的重要应用，能够自动执行
        预设的合约条款，减少中介成本，提高交易效率。
        """
        
        # 1. 文档摄取测试
        print("📝 测试文档摄取...")
        start_time = time.time()
        
        doc = db.ingest_text(
            content=test_content,
            filename=f"blockchain_test_{model_name}.txt",
            metadata={"模型": model_name, "测试时间": time.strftime("%Y-%m-%d %H:%M:%S")}
        )
        
        ingest_time = time.time() - start_time
        print(f"   ⏱️  摄取耗时: {ingest_time:.2f}秒")
        print(f"   📄 文档ID: {doc.external_id}")
        
        # 等待索引完成
        time.sleep(3)
        
        # 2. 检索测试
        print("\n🔍 测试向量检索...")
        start_time = time.time()
        
        retrieval_results = db.retrieve(
            query="区块链技术的特点和应用领域",
            k=3
        )
        
        retrieval_time = time.time() - start_time
        print(f"   ⏱️  检索耗时: {retrieval_time:.2f}秒")
        print(f"   📊 检索到 {len(retrieval_results.chunks)} 个文档块")
        
        if retrieval_results.chunks:
            best_match = retrieval_results.chunks[0]
            print(f"   🎯 最佳匹配相关度: {best_match.score:.4f}")
        
        # 3. RAG查询测试
        print("\n💬 测试RAG查询...")
        start_time = time.time()
        
        query_response = db.query(
            query="请详细介绍区块链技术的核心特点和主要应用领域",
            k=2,
            temperature=0.7
        )
        
        query_time = time.time() - start_time
        print(f"   ⏱️  查询耗时: {query_time:.2f}秒")
        print(f"   📝 回答长度: {len(query_response.completion)}字符")
        print(f"   📚 使用了 {len(query_response.chunks)} 个文档块")
        
        # 显示回答内容
        print(f"\n📄 {model_name} 模型回答:")
        print("-" * 40)
        print(query_response.completion[:300] + "..." if len(query_response.completion) > 300 else query_response.completion)
        print("-" * 40)
        
        db.close()
        
        return {
            "model": model_name,
            "ingest_time": ingest_time,
            "retrieval_time": retrieval_time,
            "query_time": query_time,
            "total_time": ingest_time + retrieval_time + query_time,
            "answer_length": len(query_response.completion),
            "chunks_used": len(query_response.chunks),
            "best_score": best_match.score if retrieval_results.chunks else 0,
            "answer": query_response.completion
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def compare_models():
    """对比两种模型"""
    print("🎯 Morphik 模型性能对比演示")
    print("=" * 60)
    print("💡 此演示将测试本地模型和在线模型的性能差异")
    print()
    
    results = {}
    
    # 测试本地模型
    print("🏠 测试本地模型 (Ollama)")
    if run_model_switch("local"):
        time.sleep(5)  # 等待服务稳定
        results["local"] = test_model_performance("本地模型")
    
    print("\n" + "="*60)
    
    # 测试在线模型
    print("☁️  测试在线模型 (通义千问)")
    if run_model_switch("online"):
        time.sleep(5)  # 等待服务稳定
        results["online"] = test_model_performance("在线模型")
    
    # 性能对比
    if len(results) == 2:
        print("\n" + "="*60)
        print("📊 性能对比结果")
        print("="*60)
        
        local_result = results["local"]
        online_result = results["online"]
        
        print(f"{'指标':<15} {'本地模型':<15} {'在线模型':<15} {'差异':<15}")
        print("-" * 65)
        
        # 时间对比
        print(f"{'摄取时间':<15} {local_result['ingest_time']:<15.2f} {online_result['ingest_time']:<15.2f} {online_result['ingest_time']-local_result['ingest_time']:+.2f}秒")
        print(f"{'检索时间':<15} {local_result['retrieval_time']:<15.2f} {online_result['retrieval_time']:<15.2f} {online_result['retrieval_time']-local_result['retrieval_time']:+.2f}秒")
        print(f"{'查询时间':<15} {local_result['query_time']:<15.2f} {online_result['query_time']:<15.2f} {online_result['query_time']-local_result['query_time']:+.2f}秒")
        print(f"{'总时间':<15} {local_result['total_time']:<15.2f} {online_result['total_time']:<15.2f} {online_result['total_time']-local_result['total_time']:+.2f}秒")
        
        print()
        
        # 质量对比
        print(f"{'回答长度':<15} {local_result['answer_length']:<15} {online_result['answer_length']:<15} {online_result['answer_length']-local_result['answer_length']:+}字符")
        print(f"{'最佳相关度':<15} {local_result['best_score']:<15.4f} {online_result['best_score']:<15.4f} {online_result['best_score']-local_result['best_score']:+.4f}")
        
        print("\n🎯 总结:")
        if local_result['total_time'] < online_result['total_time']:
            print("⚡ 本地模型在响应速度方面更快")
        else:
            print("⚡ 在线模型在响应速度方面更快")
            
        if online_result['answer_length'] > local_result['answer_length']:
            print("📝 在线模型提供了更详细的回答")
        else:
            print("📝 本地模型提供了更简洁的回答")
            
        print("\n💡 建议:")
        print("   - 本地模型: 适合快速响应、隐私敏感的场景")
        print("   - 在线模型: 适合需要高质量输出的复杂任务")
    
    print(f"\n🎉 演示完成！")
    return results

def main():
    """主函数"""
    print("欢迎使用 Morphik 模型对比演示！")
    print()
    
    # 检查当前目录
    if not os.path.exists("../code/smart_model_switch.py"):
        print("❌ 请在 morphik-Demo/code 目录下运行此脚本")
        sys.exit(1)
    
    try:
        results = compare_models()
        
        # 保存结果到文件
        if results:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            with open(f"model_comparison_{timestamp}.txt", "w", encoding="utf-8") as f:
                f.write("Morphik 模型对比结果\n")
                f.write("=" * 30 + "\n")
                f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                for model_type, result in results.items():
                    if result:
                        f.write(f"{result['model']} 结果:\n")
                        f.write(f"  摄取时间: {result['ingest_time']:.2f}秒\n")
                        f.write(f"  检索时间: {result['retrieval_time']:.2f}秒\n")
                        f.write(f"  查询时间: {result['query_time']:.2f}秒\n")
                        f.write(f"  总时间: {result['total_time']:.2f}秒\n")
                        f.write(f"  回答长度: {result['answer_length']}字符\n")
                        f.write(f"  最佳相关度: {result['best_score']:.4f}\n")
                        f.write(f"  回答内容: {result['answer']}\n\n")
            
            print(f"📄 详细结果已保存到: model_comparison_{timestamp}.txt")
    
    except KeyboardInterrupt:
        print("\n⚠️  演示被用户中断")
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")

if __name__ == "__main__":
    main()
