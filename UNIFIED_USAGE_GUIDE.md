# 🎯 Morphik 统一管理系统使用指南

## 🚀 快速开始

### 一键智能模型切换
```bash
# 切换到本地模型 (隐私安全，免费使用)
./manage_services.sh smart-local

# 切换到在线模型 (高质量输出，强大能力)
./manage_services.sh smart-online
```

### 测试功能
```bash
# 验证系统状态
python code/verify_system.py

# 运行完整功能测试
cd code && python 1.py
```

## 📋 完整命令列表

### 🎯 智能模型切换 (推荐)
| 命令 | 功能 | 说明 |
|------|------|------|
| `smart-local` | 智能切换到本地模型 | 自动处理配置+数据库+重启 |
| `smart-online` | 智能切换到在线模型 | 自动处理配置+数据库+重启 |

### 🔧 服务管理
| 命令 | 功能 | 说明 |
|------|------|------|
| `start` | 启动所有服务 | 基础服务 + Morphik API |
| `stop` | 停止所有服务 | 包括 Docker 容器和进程 |
| `restart` | 重启所有服务 | 完整的停止和启动流程 |
| `status` | 显示服务状态 | 详细的状态检查 |
| `logs` | 显示服务日志 | 查看运行日志 |

### 🔄 基础模型切换 (兼容性)
| 命令 | 功能 | 说明 |
|------|------|------|
| `local [restart]` | 切换到本地模型 | 仅更新配置文件 |
| `online [restart]` | 切换到在线模型 | 仅更新配置文件 |
| `model-status` | 显示模型配置 | 查看当前模型状态 |

### 📋 其他命令
| 命令 | 功能 | 说明 |
|------|------|------|
| `help` | 显示帮助信息 | 完整的命令说明 |

## 🎯 智能切换 vs 基础切换

### 🤖 智能切换 (推荐)
- ✅ **自动配置管理** - 更新所有相关配置
- ✅ **智能维度处理** - 自动创建正确维度的向量表
- ✅ **完整服务重启** - 确保所有更改生效
- ✅ **错误自动处理** - 智能检测和修复问题
- ✅ **一键完成** - 无需额外操作

### 🔧 基础切换 (兼容性)
- ⚠️ **仅更新配置** - 只修改配置文件
- ⚠️ **手动重启** - 需要手动重启服务
- ⚠️ **手动维度管理** - 可能需要手动处理数据库
- ⚠️ **多步操作** - 需要多个命令完成

## 📊 模型对比

### 🏠 本地模型 (Ollama)
| 特性 | 表现 |
|------|------|
| **隐私性** | 🟢 完全本地，数据不上传 |
| **成本** | 🟢 完全免费 |
| **网络依赖** | 🟢 无需网络连接 |
| **响应速度** | 🟡 2-5秒 |
| **输出质量** | 🟡 中等 |
| **向量维度** | 768维 |

### ☁️ 在线模型 (通义千问)
| 特性 | 表现 |
|------|------|
| **隐私性** | 🟡 数据上传到云端 |
| **成本** | 🟡 按调用计费 |
| **网络依赖** | 🔴 需要稳定网络 |
| **响应速度** | 🟡 3-8秒 |
| **输出质量** | 🟢 高质量 |
| **向量维度** | 1024维 |

## 🎯 使用场景建议

### 选择本地模型的场景
- 🔒 **敏感数据处理** - 医疗、法律、金融等隐私敏感领域
- 💰 **成本控制** - 需要控制API调用费用
- 🌐 **网络限制** - 网络不稳定或离线环境
- ⚡ **快速响应** - 简单任务需要快速处理
- 🔧 **开发测试** - 开发阶段的快速迭代

### 选择在线模型的场景
- 🧠 **高质量输出** - 需要高质量的文本生成
- 📚 **复杂推理** - 复杂的知识问答和分析
- 🎨 **创意内容** - 创意写作和内容创作
- 📊 **深度分析** - 复杂的数据分析和报告
- 🎯 **生产环境** - 面向用户的正式应用

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 智能切换失败
```bash
# 检查 Python 依赖
python3 -c "import asyncpg"

# 手动安装依赖
pip3 install asyncpg

# 使用基础切换作为备选
./manage_services.sh local restart
```

#### 2. 服务启动失败
```bash
# 查看详细日志
tail -f logs/morphik_api.log

# 检查端口占用
lsof -i :8000

# 强制重启
./manage_services.sh stop
./manage_services.sh start
```

#### 3. 向量维度错误
```bash
# 手动重置向量表
python code/vector_dimension_manager.py ensure 768   # 本地模型
python code/vector_dimension_manager.py ensure 1024  # 在线模型

# 完全重置 (慎用)
python code/complete_reset_1024.py
```

#### 4. 模型切换不生效
```bash
# 检查当前配置
./manage_services.sh model-status

# 重新运行智能切换
./manage_services.sh smart-local   # 或 smart-online
```

## 📈 最佳实践

### 1. 日常使用流程
```bash
# 1. 启动系统
./manage_services.sh start

# 2. 根据需求选择模型
./manage_services.sh smart-local    # 开发/测试
./manage_services.sh smart-online   # 生产/高质量需求

# 3. 验证功能
python code/verify_system.py

# 4. 运行应用
cd code && python 1.py
```

### 2. 开发建议
- 🔄 **开发阶段**: 使用本地模型进行快速迭代
- 🧪 **测试阶段**: 使用在线模型验证最终效果
- 🚀 **生产部署**: 根据具体需求选择合适模型
- 📊 **性能监控**: 定期对比两种模型的表现

### 3. 维护建议
- 📋 **定期检查**: 运行 `./manage_services.sh status`
- 🔍 **日志监控**: 查看 `logs/morphik_api.log`
- 🔄 **系统验证**: 定期运行 `python code/verify_system.py`
- 💾 **数据备份**: 重要数据的定期备份

## 📞 技术支持

### 获取帮助
```bash
# 查看完整帮助
./manage_services.sh help

# 系统状态检查
./manage_services.sh status

# 模型配置检查
./manage_services.sh model-status
```

### 日志文件位置
- **API日志**: `logs/morphik_api.log`
- **Worker日志**: `logs/worker.log`
- **摄取日志**: `logs/worker_ingestion.log`

---

## 🎉 **享受您的 Morphik 统一管理系统！**

现在您拥有了一个功能完整、操作简单、稳定可靠的双模型 RAG 系统。无论是隐私敏感的本地处理，还是高质量的在线服务，都能轻松切换使用！ 🚀
