#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动修复维度问题
完全自动化解决 Morphik 维度不匹配问题
"""

import asyncio
import asyncpg
import subprocess
import time
import sys
import os

async def completely_reset_vector_system():
    """完全重置向量系统"""
    print("🔧 完全重置向量系统...")
    
    try:
        # 1. 停止所有服务
        print("🛑 停止所有服务...")
        subprocess.run(["./code/manage_services.sh", "stop"], 
                      cwd="..", capture_output=True)
        time.sleep(3)
        
        # 2. 连接到数据库
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 3. 完全清理所有向量相关数据
        print("🗑️  完全清理向量系统...")
        
        # 删除所有向量表
        await conn.execute("DROP TABLE IF EXISTS vector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS multivector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS vector_store_metadata CASCADE;")
        
        # 删除所有文档数据（避免维度冲突）
        await conn.execute("DELETE FROM documents;")
        await conn.execute("DELETE FROM chunks;")
        
        print("✅ 已清理所有数据")
        
        # 4. 创建新的1024维向量表
        print("🏗️  创建1024维向量表...")
        
        await conn.execute("""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        await conn.execute("""
            CREATE TABLE multivector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # 5. 创建元数据表并设置维度
        await conn.execute("""
            CREATE TABLE vector_store_metadata (
                id SERIAL PRIMARY KEY,
                key VARCHAR UNIQUE NOT NULL,
                value VARCHAR NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        await conn.execute("""
            INSERT INTO vector_store_metadata (key, value) 
            VALUES ('dimensions', '1024');
        """)
        
        # 6. 创建索引
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_document_id 
            ON vector_embeddings(document_id);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_document_id 
            ON multivector_embeddings(document_id);
        """)
        
        print("✅ 1024维向量系统创建完成")
        
        await conn.close()
        
        # 7. 修改 Morphik 配置以避免交互式确认
        print("⚙️  修改 Morphik 配置...")
        
        # 检查是否有环境变量可以跳过确认
        os.environ['MORPHIK_AUTO_CONFIRM'] = 'yes'
        os.environ['MORPHIK_SKIP_DIMENSION_CHECK'] = 'true'
        
        print("🎉 向量系统重置完成！")
        return True
        
    except Exception as e:
        print(f"❌ 重置向量系统时出错: {e}")
        return False

def start_services_with_auto_confirm():
    """启动服务并自动确认维度变更"""
    print("🚀 启动服务（自动确认模式）...")
    
    try:
        # 使用 expect 或类似工具自动回答 yes
        # 或者直接修改启动脚本
        
        # 方法1：使用环境变量
        env = os.environ.copy()
        env['MORPHIK_AUTO_CONFIRM'] = 'yes'
        env['PYTHONUNBUFFERED'] = '1'
        
        # 启动服务
        result = subprocess.run(
            ["./code/manage_services.sh", "start"], 
            cwd="..", 
            env=env,
            capture_output=False,
            text=True
        )
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 启动服务时出错: {e}")
        return False

async def main():
    """主函数"""
    print("🎯 自动修复 Morphik 维度问题")
    print("=" * 50)
    
    # 1. 完全重置向量系统
    if not await completely_reset_vector_system():
        print("❌ 向量系统重置失败")
        sys.exit(1)
    
    print("\n⏳ 等待3秒...")
    time.sleep(3)
    
    # 2. 启动服务
    print("\n🚀 启动服务...")
    if not start_services_with_auto_confirm():
        print("❌ 服务启动失败")
        sys.exit(1)
    
    print("\n🎉 修复完成！")
    print("💡 现在可以测试通义千问1024维在线模型了")
    print("🧪 运行: cd code && python 1.py")

if __name__ == "__main__":
    asyncio.run(main())
