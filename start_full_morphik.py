#!/usr/bin/env python3
"""
完整的 Morphik 服务器启动脚本
跳过 Redis 容器管理，使用现有的 Redis 服务
"""

import argparse
import atexit
import logging
import os
import signal
import socket
import subprocess
import sys
import time

import uvicorn
from dotenv import load_dotenv

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Global variable to store the worker process
worker_process = None


def wait_for_redis(host="localhost", port=6379, timeout=20):
    """
    Wait for Redis to become available.
    """
    logging.info(f"Waiting for Redis to be available at {host}:{port}...")
    t0 = time.monotonic()
    while time.monotonic() - t0 < timeout:
        try:
            with socket.create_connection((host, port), timeout=1):
                logging.info("Redis is accepting connections.")
                return True
        except (OSError, socket.error):
            logging.debug(f"Redis not available yet, retrying... ({int(time.monotonic() - t0)}s elapsed)")
            time.sleep(0.3)

    logging.error(f"Redis not reachable after {timeout}s")
    return False


def wait_for_postgres(host="localhost", port=5433, timeout=20):
    """
    Wait for PostgreSQL to become available.
    """
    logging.info(f"Waiting for PostgreSQL to be available at {host}:{port}...")
    t0 = time.monotonic()
    while time.monotonic() - t0 < timeout:
        try:
            with socket.create_connection((host, port), timeout=1):
                logging.info("PostgreSQL is accepting connections.")
                return True
        except (OSError, socket.error):
            logging.debug(f"PostgreSQL not available yet, retrying... ({int(time.monotonic() - t0)}s elapsed)")
            time.sleep(0.3)

    logging.error(f"PostgreSQL not reachable after {timeout}s")
    return False


def start_arq_worker():
    """Start the ARQ worker as a subprocess."""
    global worker_process
    try:
        logging.info("Starting ARQ worker...")

        # Ensure logs directory exists
        log_dir = os.path.join(os.getcwd(), "logs")
        os.makedirs(log_dir, exist_ok=True)

        # Worker log file paths
        worker_log_path = os.path.join(log_dir, "worker.log")

        # Open log files
        worker_log = open(worker_log_path, "a")

        # Add timestamp to log
        timestamp = subprocess.check_output(["date"]).decode().strip()
        worker_log.write(f"\n\n--- Worker started at {timestamp} ---\n\n")
        worker_log.flush()

        # Use sys.executable to ensure the same Python environment is used
        worker_cmd = [sys.executable, "-m", "arq", "core.workers.ingestion_worker.WorkerSettings"]

        # Start the worker with output redirected to log files
        worker_process = subprocess.Popen(
            worker_cmd,
            stdout=worker_log,
            stderr=worker_log,
            env=dict(os.environ, PYTHONUNBUFFERED="1"),  # Ensure unbuffered output
        )
        logging.info(f"ARQ worker started with PID: {worker_process.pid}")
        logging.info(f"Worker logs available at: {worker_log_path}")
    except Exception as e:
        logging.error(f"Failed to start ARQ worker: {e}")
        # Don't exit, continue without worker for basic functionality


def cleanup_processes():
    """Stop the ARQ worker process on exit."""
    global worker_process
    if worker_process and worker_process.poll() is None:  # Check if process is still running
        logging.info(f"Stopping ARQ worker (PID: {worker_process.pid})...")

        # Send SIGTERM first for graceful shutdown
        worker_process.terminate()
        try:
            # Wait a bit for graceful shutdown
            worker_process.wait(timeout=5)
            logging.info("ARQ worker stopped gracefully.")
        except subprocess.TimeoutExpired:
            logging.warning("ARQ worker did not terminate gracefully, sending SIGKILL.")
            worker_process.kill()  # Force kill if it doesn't stop
            logging.info("ARQ worker killed.")


# Register the cleanup function to be called on script exit
atexit.register(cleanup_processes)
# Also register for SIGINT (Ctrl+C) and SIGTERM
signal.signal(signal.SIGINT, lambda sig, frame: sys.exit(0))
signal.signal(signal.SIGTERM, lambda sig, frame: sys.exit(0))


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Start the full Morphik server")
    parser.add_argument(
        "--log",
        choices=["debug", "info", "warning", "error"],
        default="info",
        help="Set the logging level",
    )
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="Host to bind the server to",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to",
    )
    parser.add_argument(
        "--skip-worker",
        action="store_true",
        help="Skip starting the ARQ worker",
    )
    args = parser.parse_args()

    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log.upper()))

    # Load environment variables from .env file
    load_dotenv()

    # Check if basic services are available (skip Redis container management)
    if not wait_for_redis():
        logging.error("Cannot start server without Redis. Please ensure Redis is running on port 6379.")
        sys.exit(1)

    if not wait_for_postgres():
        logging.error("Cannot start server without PostgreSQL. Please ensure PostgreSQL is running on port 5433.")
        sys.exit(1)

    # Start ARQ worker in the background (optional)
    if not args.skip_worker:
        start_arq_worker()
    else:
        logging.info("Skipping ARQ worker startup")

    # Start server (this is blocking)
    logging.info(f"Starting full Morphik server on {args.host}:{args.port}...")
    
    try:
        uvicorn.run(
            "core.api:app",
            host=args.host,
            port=args.port,
            loop="asyncio",
            log_level=args.log,
        )
    except Exception as e:
        logging.error(f"Failed to start Morphik server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
