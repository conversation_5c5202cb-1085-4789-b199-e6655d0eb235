# 🎯 Morphik 智能模型切换系统

## 📋 概述

本系统实现了 Morphik 在本地模型和在线模型之间的智能切换，完美解决了向量维度不匹配的问题。

### 🏠 本地模型配置
- **LLM**: Ollama qwen3:4b
- **嵌入模型**: nomic-embed-text
- **向量维度**: 768维
- **优势**: 隐私安全、无API费用、响应快速

### ☁️ 在线模型配置  
- **LLM**: 阿里云通义千问 qwen-max-latest
- **嵌入模型**: text-embedding-v3
- **向量维度**: 1024维
- **优势**: 能力强大、质量更高、持续优化

## 🚀 快速开始

### 一键切换模型

```bash
# 切换到本地模型 (768维)
python code/smart_model_switch.py local

# 切换到在线模型 (1024维)
python code/smart_model_switch.py online
```

### 测试模型功能

```bash
# 运行完整测试
cd code && python 1.py

# 快速功能验证
cd code && python -c "
from morphik import Morphik
db = Morphik(is_local=True)
doc = db.ingest_text('测试文档', 'test.txt')
result = db.query('这是什么？')
print(f'回答: {result.completion}')
db.close()
"
```

## 🛠️ 核心工具

### 1. 智能模型切换工具
- **文件**: `code/smart_model_switch.py`
- **功能**: 自动处理配置、数据库、服务重启
- **用法**: `python smart_model_switch.py [local|online]`

### 2. 维度修复工具
- **768维修复**: `code/fix_768_dimensions.py`
- **1024维修复**: `code/fix_1024_dimensions.py`
- **表结构检查**: `code/check_table_structure.py`

### 3. 性能对比工具
- **文件**: `code/model_comparison_demo.py`
- **功能**: 对比两种模型的性能差异
- **用法**: `cd code && python model_comparison_demo.py`

## 📊 服务管理

### 服务状态检查
```bash
./manage_services.sh status          # 检查所有服务
./manage_services.sh model-status    # 检查当前模型配置
```

### 手动服务控制
```bash
./manage_services.sh start           # 启动服务
./manage_services.sh stop            # 停止服务  
./manage_services.sh restart         # 重启服务
```

### 模型切换 (传统方式)
```bash
./manage_services.sh local restart   # 切换到本地模型
./manage_services.sh online restart  # 切换到在线模型
```

## 🔧 故障排除

### 常见问题

#### 1. 向量维度错误
```
Error: expected 768 dimensions, not 1024
```
**解决方案**:
```bash
# 检查当前表结构
cd code && python check_table_structure.py

# 使用智能切换工具重新配置
python code/smart_model_switch.py [local|online]
```

#### 2. 服务启动失败
**解决方案**:
```bash
# 查看日志
tail -f logs/morphik_api.log

# 强制重启
./manage_services.sh stop
docker-compose down
./manage_services.sh start
```

#### 3. 模型切换不生效
**解决方案**:
```bash
# 检查配置文件
cat morphik.toml | grep -A 3 "\[completion\]"
cat morphik.toml | grep -A 3 "\[embedding\]"

# 重新运行智能切换
python code/smart_model_switch.py [target_model]
```

### 日志文件位置
- **API日志**: `logs/morphik_api.log`
- **Worker日志**: `logs/worker.log`
- **摄取日志**: `logs/worker_ingestion.log`

## 📈 性能特点

### 本地模型 (Ollama)
| 指标 | 表现 |
|------|------|
| 响应时间 | 2-5秒 |
| 隐私性 | 完全本地 |
| 成本 | 免费 |
| 质量 | 中等 |
| 网络依赖 | 无 |

### 在线模型 (通义千问)
| 指标 | 表现 |
|------|------|
| 响应时间 | 3-8秒 |
| 隐私性 | 云端处理 |
| 成本 | 按调用计费 |
| 质量 | 高 |
| 网络依赖 | 需要 |

## 🎯 使用建议

### 选择本地模型的场景
- 🔒 处理敏感数据
- 💰 控制API成本
- 🌐 网络不稳定
- ⚡ 快速响应需求

### 选择在线模型的场景
- 🧠 高质量输出需求
- 📚 复杂推理任务
- 🎨 创意内容生成
- 📊 深度数据分析

## 🔄 最佳实践

1. **开发阶段**: 使用本地模型快速迭代
2. **测试验证**: 使用在线模型验证效果
3. **生产部署**: 根据需求选择合适模型
4. **定期对比**: 评估两种模型的表现

## 📚 文档结构

```
code/
├── smart_model_switch.py      # 智能模型切换工具
├── fix_768_dimensions.py      # 768维修复工具
├── fix_1024_dimensions.py     # 1024维修复工具
├── check_table_structure.py   # 表结构检查工具
├── model_comparison_demo.py   # 性能对比演示
├── 1.py                       # 功能测试脚本
├── SOLUTION_SUMMARY.md        # 解决方案总结
├── QUICK_START_GUIDE.md       # 快速开始指南
└── README_MODEL_SWITCHING.md  # 本文档
```

## 🎉 成功案例

✅ **问题**: 向量维度不匹配导致文档摄取失败  
✅ **解决**: 智能切换工具自动处理维度适配  
✅ **结果**: 两种模型都能正常工作，无缝切换  

✅ **问题**: 手动配置复杂且容易出错  
✅ **解决**: 一键切换脚本自动化所有步骤  
✅ **结果**: 用户只需一条命令即可完成切换  

✅ **问题**: 服务重启后配置不生效  
✅ **解决**: 完整的服务重启和缓存清理流程  
✅ **结果**: 配置更改立即生效，稳定可靠  

## 💡 技术亮点

- 🎯 **智能维度适配**: 自动检测并创建正确维度的向量表
- 🔄 **无缝模型切换**: 一键完成配置、数据库、服务的全套更新
- 🛡️ **错误恢复机制**: 完善的故障检测和自动修复功能
- 📊 **性能监控**: 内置的性能对比和测试工具
- 📚 **完整文档**: 详细的使用指南和故障排除手册

---

**🎉 恭喜！您现在拥有了一个完全可用的 Morphik 双模型切换系统！**

如有任何问题，请参考故障排除部分或查看相关日志文件。
