# Morphik 统一管理脚本使用说明

## 📋 概述

`manage_services.sh` 是一个统一的 Morphik 管理脚本，集成了以下功能：
- **服务管理**：启动、停止、重启、状态查看
- **模型切换**：本地模型与在线模型之间的切换
- **日志查看**：服务运行日志

## 🚀 功能特性

### ✅ **已合并的功能**
1. **原 `manage_services.sh` 的所有功能**
2. **原 `switch_model.sh` 的所有功能**
3. **增强的错误处理和用户体验**
4. **统一的命令行界面**

## 📍 使用位置

**重要**：此脚本应该在 **项目根目录** 运行，而不是在 `code` 目录中：

```bash
# 正确的使用方式 - 在项目根目录
cd /path/to/morphik-Demo
./code/manage_services.sh [命令]

# 错误的使用方式 - 在 code 目录中
cd /path/to/morphik-Demo/code
./manage_services.sh [命令]  # ❌ 会找不到配置文件
```

## 🔧 命令列表

### **服务管理命令**
```bash
./code/manage_services.sh start              # 启动所有服务
./code/manage_services.sh stop               # 停止所有服务
./code/manage_services.sh restart            # 重启所有服务
./code/manage_services.sh status             # 查看详细服务状态
./code/manage_services.sh logs               # 显示服务日志
```

### **模型切换命令**
```bash
./code/manage_services.sh local              # 切换到本地模型
./code/manage_services.sh local restart      # 切换到本地模型并重启服务
./code/manage_services.sh online             # 切换到在线模型
./code/manage_services.sh online restart     # 切换到在线模型并重启服务
./code/manage_services.sh model-status       # 查看当前模型配置
```

### **帮助命令**
```bash
./code/manage_services.sh help               # 显示帮助信息
./code/manage_services.sh                    # 默认显示帮助
```

## 🔄 模型配置详情

### **本地模型配置**
- **LLM**: `ollama_qwen3_4b` (Gemma 3 4B)
- **嵌入模型**: `ollama_embedding_local` (Nomic Embed)
- **向量维度**: 768维
- **优点**: 免费、隐私、离线可用
- **缺点**: 性能相对较低、需要本地资源

### **在线模型配置**
- **LLM**: `qwen_max_online` (通义千问 Max)
- **嵌入模型**: `qwen_embedding_online` (Text Embedding V3)
- **向量维度**: 1024维
- **优点**: 性能强大、中文理解优秀
- **缺点**: 需要API密钥、网络连接

## 📊 使用示例

### **1. 查看当前状态**
```bash
# 查看服务状态
./code/manage_services.sh status

# 查看模型配置
./code/manage_services.sh model-status
```

### **2. 切换到本地模型开发**
```bash
# 切换到本地模型（节省API费用）
./code/manage_services.sh local restart

# 验证切换结果
./code/manage_services.sh model-status
```

### **3. 切换到在线模型生产**
```bash
# 切换到在线模型（更好的性能）
./code/manage_services.sh online restart

# 验证切换结果
./code/manage_services.sh model-status
```

### **4. 日常服务管理**
```bash
# 启动服务
./code/manage_services.sh start

# 查看运行状态
./code/manage_services.sh status

# 查看日志（如果有问题）
./code/manage_services.sh logs

# 重启服务（解决问题）
./code/manage_services.sh restart
```

## ⚠️ 注意事项

### **1. 运行位置**
- 必须在项目根目录运行
- 脚本会自动查找 `morphik.toml` 配置文件
- 如果在错误位置运行会提示找不到配置文件

### **2. 模型切换**
- 切换模型后建议重启服务以确保配置生效
- 使用 `restart` 参数可以自动重启
- 维度变化会自动处理（768 ↔ 1024）

### **3. 配置备份**
- 首次运行会自动创建 `morphik.toml.backup`
- 如果配置出错可以从备份恢复

### **4. 依赖检查**
- 脚本会自动检查 Docker 是否运行
- 会检查必要的端口是否可用
- 会验证 Ollama 服务状态（本地模型需要）

## 🔧 故障排除

### **找不到配置文件**
```bash
错误: 找不到 ../morphik.toml 文件
```
**解决方案**: 确保在项目根目录运行脚本

### **服务启动失败**
```bash
❌ Morphik API 启动失败
```
**解决方案**: 
1. 检查日志：`./code/manage_services.sh logs`
2. 确保基础服务运行：`./code/manage_services.sh status`
3. 重启服务：`./code/manage_services.sh restart`

### **模型切换后问题**
**解决方案**:
1. 确保重启了服务：`./code/manage_services.sh restart`
2. 检查模型状态：`./code/manage_services.sh model-status`
3. 查看服务状态：`./code/manage_services.sh status`

## 🎯 最佳实践

1. **开发阶段**: 使用本地模型节省成本
   ```bash
   ./code/manage_services.sh local restart
   ```

2. **测试阶段**: 切换到在线模型验证性能
   ```bash
   ./code/manage_services.sh online restart
   ```

3. **生产部署**: 根据需求选择合适的模型
   ```bash
   # 高性能需求
   ./code/manage_services.sh online restart
   
   # 成本敏感
   ./code/manage_services.sh local restart
   ```

4. **定期维护**: 检查服务状态
   ```bash
   ./code/manage_services.sh status
   ```

## 📝 更新日志

- **v1.0**: 合并了 `manage_services.sh` 和 `switch_model.sh`
- **v1.1**: 修复了维度切换问题（768 ↔ 1024）
- **v1.2**: 改进了错误处理和用户提示
- **v1.3**: 添加了自动重启功能

---

**提示**: 如果您有任何问题或建议，请查看脚本的帮助信息：
```bash
./code/manage_services.sh help
```
