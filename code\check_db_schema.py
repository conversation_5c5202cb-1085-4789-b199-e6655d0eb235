#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库表结构脚本
"""

import asyncio
import asyncpg

async def check_database_schema():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构...")
    
    try:
        # 连接到 PostgreSQL
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 检查所有表
        print("\n📋 所有表:")
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # 检查向量表结构
        print("\n🔍 检查 vector_embeddings 表结构:")
        if any(table['table_name'] == 'vector_embeddings' for table in tables):
            columns = await conn.fetch("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns 
                WHERE table_name = 'vector_embeddings'
                ORDER BY ordinal_position;
            """)
            
            for col in columns:
                print(f"  - {col['column_name']}: {col['data_type']}")
                if col['column_name'] == 'embedding':
                    # 检查向量维度
                    vector_info = await conn.fetchrow("""
                        SELECT atttypmod 
                        FROM pg_attribute 
                        WHERE attrelid = 'vector_embeddings'::regclass 
                        AND attname = 'embedding';
                    """)
                    if vector_info and vector_info['atttypmod'] != -1:
                        dimensions = vector_info['atttypmod']
                        print(f"    向量维度: {dimensions}")
                    else:
                        print(f"    向量维度: 未指定或动态")
        else:
            print("  ❌ vector_embeddings 表不存在")
        
        await conn.close()
        print("\n✅ 检查完成")
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

if __name__ == "__main__":
    asyncio.run(check_database_schema())
