#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完全重置数据库脚本
删除所有表并让 Morphik 重新创建
"""

import asyncio
import asyncpg
import sys

async def complete_database_reset():
    """完全重置数据库"""
    print("🔄 完全重置数据库...")
    
    try:
        # 连接到 PostgreSQL
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 获取所有表
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        """)
        
        print(f"📋 找到 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table['table_name']}")
        
        # 删除所有表
        print("\n🗑️  删除所有表...")
        for table in tables:
            table_name = table['table_name']
            try:
                await conn.execute(f"DROP TABLE IF EXISTS {table_name} CASCADE;")
                print(f"  ✅ 已删除: {table_name}")
            except Exception as e:
                print(f"  ⚠️  删除 {table_name} 时出错: {e}")
        
        # 删除所有序列
        print("\n🗑️  删除所有序列...")
        sequences = await conn.fetch("""
            SELECT sequence_name 
            FROM information_schema.sequences 
            WHERE sequence_schema = 'public'
            ORDER BY sequence_name;
        """)
        
        for seq in sequences:
            seq_name = seq['sequence_name']
            try:
                await conn.execute(f"DROP SEQUENCE IF EXISTS {seq_name} CASCADE;")
                print(f"  ✅ 已删除序列: {seq_name}")
            except Exception as e:
                print(f"  ⚠️  删除序列 {seq_name} 时出错: {e}")
        
        # 确保 pgvector 扩展存在
        print("\n🔧 确保 pgvector 扩展存在...")
        await conn.execute("CREATE EXTENSION IF NOT EXISTS vector;")
        print("  ✅ pgvector 扩展已启用")
        
        await conn.close()
        print("\n🎉 数据库完全重置完成！")
        print("📝 注意：所有数据已被删除，Morphik 将在下次启动时重新创建表结构")
        
    except Exception as e:
        print(f"❌ 重置数据库时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(complete_database_reset())
