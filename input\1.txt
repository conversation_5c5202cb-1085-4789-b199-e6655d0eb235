Lesson 8.3 ID3、C4.5 决策树的建模流程
ID3 和 C4.5 作为的经典决策树算法，尽管⽆法通过 sklearn 来进⾏建模，但其基本原理仍然
值得讨论
与学习。接下来我们详细介绍关于 ID3 和 C4.5 这两种决策树模型的建模基本思路和原理。
ID3 和 C4.5 的基
本建模流程和 CART 树是类似的，也是根据纯度评估指标选取最佳的数据集划分⽅式，只是
不过 ID3 和
C4.5 是以信息熵为评估指标，⽽数据集的离散特征划分⽅式也是⼀次展开⼀列，⽽不是寻
找切点进⾏切
分。我们先从 ID3 的基本原理开始介绍，随后讨论 C4.5 在 ID3 基础上的改善措施。
import numpy as np
from ML_basic_function import *
⼀、ID3 决策树的基本建模流程
ID3 是⼀个只能围绕离散型变量进⾏分类问题建模的决策树模型，即 ID3 ⽆法处理连续型特
征、也⽆
法处理回归问题，如果带⼊训练数据有连续型变量，则⾸先需要对其进⾏离散化处理，也就
是连续变量
分箱。例如如下个⼈消费数据，各特征都是离散型变量，能够看出，其中 age 和 income 两
列就是连续型
变量分箱之后的结果，例如 age 列就是以 30、40 为界进⾏连续变量分箱。当然，除了如下
表示外，我们
还可以将分箱之后的结果直接赋予⼀个离散的值，如 1、2、3 等。
更多关于连续变量的离散化的⽅法将在特征⼯程部分进⾏介绍。
ID3 的⽣⻓过程其实和 CART 树基本⼀致，其⽬标都是尽可能降低数据集的不纯度，其⽣⻓
的过程也
就是数据集不断划分的过程。只不过 ID3 的数据集划分过程（规律提取过程）和 CART 树有
所不同，CART
树是在所有特征中寻找切分点、然后再从中挑选出能够最⼤程度降低数据集不纯度的节分⽅
式，换⽽⾔
之就是 CART 树是按照某切分点来展开，⽽ ID3 则是按照列来展开，即根据某列的不同取值
来对数据集进
⾏划分。例如根据上述数据集中的 age 列的不同取值来对原始数据集进⾏划分，则划分结果
如下：
同样，我们可以计算在以 age 的不同取值为划分规则、对数据集进⾏划分后数据集整体不纯
度下降结果，
ID3 中采⽤信息熵作为评估指标，具体计算过程如下：
⾸先计算⽗节点的信息熵
#  ⽗节点 A 的信息熵
ent_A = -5/14 * np.log2(5/14) - 9/14 * np.log2(9/14)
ent_A
0.9402859586706311
然后计算每个⼦节点的信息熵
#  ⼦节点 B 的信息熵
ent_B1 = entropy(2/5)

ent_B2 = entropy(2/5)
ent_B3 = 0
ent_B1, ent_B2, ent_B3

(0.9709505944546686, 0.9709505944546686, 0)
同样，⼦节点整体信息熵就是每个⼦节点的信息熵加权求和计算得出，其权重就是各⼦节点
数据集数量
占⽗节点总数据量的⽐例：
ent_B = ent_B1 * 5/14 + ent_B2 * 5/14 + ent_B3 * 4/14
ent_B
0.6935361388961919
然后即可算出按照如此规则进⾏数据集划分，最终能够减少的不纯度数值：
#  不纯度下降结果
ent_A - ent_B
0.24674981977443922
⽽该结果也被称为根据 age 列进⾏数据集划分后的信息增益（information gain），上 述 结果
可写成
Gain(age) = 0.247
当然，⾄此我们只计算了按照 age 列的不同取值来进⾏数据集划分后数据集不纯度下降结
果，⽽按照
age 列进⾏展开只能算是树的第⼀步⽣⻓中的⼀个备选划分规则，此外我们还需要测试按照
income、
student 或者 credit_rating 列展开后数据集不纯度下降情况，具体计算过程和 age 列展开后
的计算过程类
似，此处直接给出结果，Gain(income)=0.026、Gain(student)=0.151、Gain(credit_rating)=0.048。
很
明显，按照 age 列展开能够更有效的降低数据集的不纯度，因此树的第⼀层⽣⻓就是按照
age 列的不同取
值对数据集进⾏划分。
接下来需要继续进⾏迭代，通过观察我们不难发现，对于数据集 B1 来说来说，按照 student
这⼀列来
进⾏展开，能够让⼦节点的信息熵归零，⽽数据集 B2 按照如果按照 credit_rating 来展开，
也同样可以将
⼦节点的标签纯度提⾼⾄ 100%。因此该模型最终树的⽣⻓形态如下：
⾄此，我们就完成了 ID3 决策树的建模全流程，具体模型结果解读和 CART 树完全⼀样，此
处不做赘
述。接下来简单对⽐ ID3 和 CART 树之间的差异：⾸先，由于 ID3 是按照列来提取规则、每
次展开⼀列，因
此每⼀步⽣⻓会有⼏个分⽀，其实完全由当前列有⼏个分类⽔平决定，⽽ CART 树只能进⾏
⼆叉树的⽣
⻓；其次，由于 ID3 每次展开⼀列，因此建模过程中对“列的消耗”⾮常快，数据集中特征个
数就决定了树
的最⼤深度，相⽐之下 CART 树的备选规则就要多的多，这也使得 CART 树能够进⾏更加精
细的规则提

取；当然，尽管 CART 树和 ID3 存在着基本理论层⾯的差异，但有的时候也能通过 CART 树
的⽅法来挖掘出
和 ID3 决策树相同的规律，例如 ID3 中按照 age 列⼀层展开所提取出的三个分类规则，也可
以在 CART 树中
通过两层树来实现，例如第⼀层按照是否是<=30 来进⾏划分、第⼆层围绕不满⾜第⼀层条
件的数据集进
⼀步根据是否>40 来进⾏划分。

此外，需要注意的是，正因为 ID3 是按照列来进⾏展开，因此只能处理特征都是离散变量的
数据集。
另外，根据 ID3 的建模规则我们不难发现，ID3 树在实际⽣⻓过程中会更倾向于挑选取值较
多的分类变量
展开，但如此⼀来便更加容易造成模型过拟合，⽽遗憾的是 ID3 并没有任何防⽌过拟合的措
施。⽽这些
ID3 的缺陷，则正是 C4.5 算法的改进⽅向。接下来我们继续讨论关于 C4.5 决策树的建模规
则。
当然，对于 ID3 来说，规则是和分类变量的取值⼀⼀绑定的，
⼆、C4.5 决策树的基本建模流程
作为 ID3 的改进版算法，C4.5 在 ID3 的基础上进⾏了三个⽅⾯的优化，⾸先在衡量不纯度
降低的数值
计算过程中引⼊信息值（information value，也被称为划分信息度、分⽀度等）概念来修正
信息熵的计
算结果，以抑制 ID3 更倾向于寻找分类⽔平较多的列来展开的情况，从⽽间接抑制模型过拟
合倾向；其⼆
则是新增了连续变量的处理⽅法，也就是 CART 树中寻找相邻取值的中间值作为切分点的⽅
法；其三是加
⼊了决策树的剪枝流程，使得模型泛化能⼒能够得到进⼀步提升。但需要注意的是，尽管有
如此改进，
但 C4.5 仍然只能解决分类问题，其本质仍然还是⼀种分类树。接下来我们详细讨论 C4.5 的
具体改进策
略。
信息值（information value）
C4.5 中信息值（以下简称 IV 值）是⼀个⽤于衡量数据集在划分时分⽀个数的指标，如果划
分时分⽀
越多，IV 值就越⾼。具体 IV 值的计算公式如下：
IV 值计算公式和信息熵的计算公式基本⼀致，只是具体计算的⽐例不再是各类样本所占⽐
例，⽽是
各划分后⼦节点的数据所占⽐例，或者说信息熵是计算标签不同取值的混乱程度，⽽ IV 值
就是计算
特征不同取值的混乱程度
其中 K 表示某次划分是总共分⽀个数，$v_i$表示划分后的某样本，$P(v_i)$表示该样本数量
占⽗节点数据
量的⽐例。对于如下三种数据集划分情况，简单计算 IV 值：
#  ⽗节点按照 50%-50%进⾏划分

- (1/2 * np.log2(1/2) + 1/2 * np.log2(1/2))
1.0
#  ⽗节点按照 1/4-1/2-1/4 进⾏划分
- (1/4 * np.log2(1/4) + 1/2 * np.log2(1/2)+ 1/4 * np.log2(1/4))
1.5

#  ⽗节点按照 1/4-1/4-1/4-1/4 进⾏划分
- (1/4 * np.log2(1/4) + 1/4 * np.log2(1/4) + 1/4 * np.log2(1/4) + 1/4 *
np.log2(1/4))
2.0
⽽在实际建模过程中，ID3 是通过信息增益的计算结果挑选划分规则，⽽ C4.5 采⽤ IV 值对
信息增益计算结
果进⾏修正，构建新的数据集划分评估指标：增益⽐例（Gain  Ratio，被称为获利⽐例或增
益率），来指
导具体的划分规则的挑选。GR 的计算公式如下：
也就是说，在 C4.5 的建模过程中，需要先计算 GR，然后选择 GR 计算结果较⼤的列来执⾏
这⼀次展
开。例如对于上述例⼦来看，以 age 列展开后 Information Gain 结果为：
IG = ent_A - ent_B
IG
0.24674981977443922
⽽ IV 值为：
IV = - (5/14 * np.log2(5/14) + 5/14 * np.log2(5/14)+ 4/14 * np.log2(4/14))
IV
1.5774062828523454
因此计算得到 GR 值为：
GR = IG / IV
GR
0.1564275624211752
然后据此进⼀步计算其他各列展开后的 GR 值，并选择 GR 较⼤者进⾏数据集划分。

C4.5 的连续变量处理⽅法
C4.5 允许带⼊连续变量进⾏建模，并且围绕连续变量的规则提取⽅式和此前介绍的 CART 树
⼀致。即
在连续变量中寻找相邻的取值的中间点作为备选切分点，通过计算切分后的 GR 值来挑选最
终数据集划分
⽅式。当然，基于连续变量的备选切分⽅式也要和离散变量的切分⽅式进⾏横向⽐较，到底
是⼀次展开
⼀个离散列还是按照连续变量的某个切点展开，要根据最终 GR 的计算结果来决定。
例如，如果将上述数据集的 age 列换成连续变量，则我们需要计算的 GR 情况就变成了
GR(income)、
GR(student)、GR(credit_rating)、GR(age<=26.5)、GR(age<=27.5)...
当然，由于 C4.5 的离散变量和连续变量提取规则⽅式不同，离散变量是⼀次消耗⼀列来进
⾏展开

（有可能多分叉），⽽连续变量则⼀次消耗⼀个切分点，因此和 CART 树⼀样、同⼀个连续
变量可
以多次指导数据集进⾏划分。
在 sklearn 的树模型介绍⽂档中，有⼀段关于 sklearn 的决策树不⽀持离散变量建模的说明，
其意为
不⽀持按照类似 ID3 或 C4.5 的⽅式直接将离散变量按列来进⾏展开，⽽是根据 sklearn 中集
成的
CART 树⾃⾝的建模规则，使得 sklearn 中的决策树实际上在处理特征时都是按照 C4.5 中连
续变量的
处理⽅式在进⾏处理，并⾮指的是带⼊离散变量就⽆法建模。


