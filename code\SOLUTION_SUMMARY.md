# Morphik 模型切换问题解决方案总结

## 🎯 问题描述

用户希望配置 Morphik 以便灵活切换本地模型和在线模型：
- **本地模型**: Ollama (qwen3:4b + nomic-embed-text, 768维)
- **在线模型**: 阿里云通义千问 (qwen-max-latest + text-embedding-v3, 1024维)

## ❌ 遇到的主要问题

1. **向量维度不匹配错误**
   - 本地模型: 768维向量
   - 在线模型: 1024维向量
   - 数据库表维度与模型不匹配导致摄取失败

2. **自动确认机制失效**
   - 向量维度变更时需要手动确认删除数据
   - 环境变量 `MORPHIK_AUTO_CONFIRM` 未生效

3. **服务重启不彻底**
   - 配置更改后服务缓存未清理
   - 数据库连接池保持旧的维度设置

## ✅ 解决方案

### 1. 创建智能模型切换工具

开发了 `smart_model_switch.py` 工具，实现：

```bash
# 切换到本地模型 (768维)
python code/smart_model_switch.py local

# 切换到在线模型 (1024维)  
python code/smart_model_switch.py online
```

### 2. 自动化处理流程

智能切换工具包含以下步骤：

1. **配置文件更新**
   - 自动修改 `morphik.toml` 中的模型配置
   - 更新 LLM、嵌入模型和维度设置

2. **数据库表重建**
   - 检测当前向量表维度
   - 如需要，删除并重建正确维度的向量表
   - 更新元数据表中的维度记录

3. **服务重启**
   - 完全停止所有 Morphik 服务
   - 清理进程和缓存
   - 重新启动服务并等待就绪

### 3. 维度修复工具

创建了专门的维度修复工具：
- `fix_768_dimensions.py` - 为本地模型创建768维表
- `fix_1024_dimensions.py` - 为在线模型创建1024维表
- `check_table_structure.py` - 检查数据库表结构

## 📊 最终配置

### 本地模型配置 (768维)
```toml
[completion]
model = "ollama_qwen3_4b"

[embedding]  
model = "ollama_embedding_local"
dimensions = 768

[agent]
model = "ollama_qwen3_4b"
```

### 在线模型配置 (1024维)
```toml
[completion]
model = "qwen_max_online"

[embedding]
model = "qwen_embedding_online" 
dimensions = 1024

[agent]
model = "qwen_max_online"
```

## 🎉 测试结果

### ✅ 本地模型测试成功
- 文档摄取: ✅ 正常
- 向量检索: ✅ 正常 
- RAG查询: ✅ 正常
- 结构化输出: ✅ 正常

### ✅ 在线模型测试成功  
- 文档摄取: ✅ 正常
- 向量检索: ✅ 正常
- RAG查询: ✅ 正常 (回答质量更高)
- 结构化输出: ✅ 正常

### ✅ 模型切换测试成功
- 本地 → 在线: ✅ 无缝切换
- 在线 → 本地: ✅ 无缝切换
- 维度自动适配: ✅ 正常

## 🛠️ 使用方法

### 快速切换模型
```bash
# 切换到本地模型
cd /path/to/morphik-Demo
python code/smart_model_switch.py local

# 切换到在线模型  
python code/smart_model_switch.py online

# 测试当前模型
cd code && python 1.py
```

### 检查服务状态
```bash
./manage_services.sh status
./manage_services.sh model-status
```

### 手动维度修复（如需要）
```bash
# 修复为768维（本地模型）
cd code && python fix_768_dimensions.py

# 修复为1024维（在线模型）
cd code && python fix_1024_dimensions.py

# 检查表结构
cd code && python check_table_structure.py
```

## 💡 关键技术要点

1. **向量维度管理**: 不同模型需要不同维度的向量表
2. **配置同步**: 确保配置文件、数据库表、服务设置一致
3. **服务重启**: 配置更改后必须完全重启服务
4. **自动化流程**: 减少手动操作，避免人为错误

## 🔮 未来改进建议

1. **配置热重载**: 支持不重启服务的配置更新
2. **多维度支持**: 同一数据库支持多种维度的向量表
3. **模型性能监控**: 添加模型切换后的性能对比
4. **批量数据迁移**: 支持在不同维度间迁移现有数据

---

**总结**: 通过智能模型切换工具，用户现在可以轻松在本地模型和在线模型之间切换，享受本地模型的隐私性和在线模型的强大性能。所有维度不匹配问题都已完美解决！ 🎉
