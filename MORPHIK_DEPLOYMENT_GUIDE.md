# Morphik 自托管 Docker 部署指南

## 📋 概述

本指南将帮助您使用 Docker Compose 部署 Morphik 项目，配置本地 Ollama 服务器，使用 qwen2.5:4b 作为主要大语言模型，使用 nomic-embed-text:latest 作为嵌入模型。

## 🎯 部署目标

- ✅ 使用 Docker Compose 部署完整的 Morphik 系统
- ✅ 配置本地 Ollama 服务器作为 LLM 提供者
- ✅ 使用 qwen2.5:4b 作为主要大语言模型
- ✅ 使用 nomic-embed-text:latest 作为嵌入模型
- ✅ 提供完整的部署和使用指南

## 🔧 系统要求

### 硬件要求
- **内存**: 至少 8GB RAM（推荐 16GB+）
- **存储**: 至少 20GB 可用磁盘空间
- **CPU**: 多核处理器（推荐 4 核+）

### 软件要求
- Docker 和 Docker Compose
- Git（用于克隆项目）

## 📦 已配置的组件

### 1. 核心服务
- **Morphik API**: 主要的 API 服务器
- **PostgreSQL**: 带 pgvector 扩展的数据库
- **Redis**: 缓存和消息队列
- **Ollama**: 本地大语言模型服务器

### 2. 模型配置
- **主要 LLM**: qwen2.5:4b（约 2.7GB）
- **嵌入模型**: nomic-embed-text:latest（约 274MB）
- **重排序器**: BAAI/bge-reranker-large

### 3. 已修改的配置文件

#### `morphik.toml` 主要更改：
- 添加了 qwen2.5:4b 模型配置
- 更新了嵌入模型为 nomic-embed-text:latest
- 配置所有组件使用 Docker 内部网络
- 设置 Redis 主机为 "redis"

#### `ollama-entrypoint.sh` 更改：
- 自动拉取 qwen2.5:4b 模型
- 自动拉取 nomic-embed-text:latest 模型

#### `.env` 文件：
- 设置了安全的 JWT 密钥
- 配置了数据库连接
- 预留了在线 API 密钥配置

## 🚀 部署步骤

### 第一步：启动基础服务

```bash
# 启动 PostgreSQL 和 Redis
docker compose up --build -d postgres redis
```

### 第二步：启动 Ollama 服务

```bash
# 启动 Ollama 服务（这将自动下载模型）
docker compose --profile ollama up -d ollama
```

**注意**: 首次启动时，Ollama 会自动下载以下模型：
- qwen2.5:4b (~2.7GB)
- nomic-embed-text:latest (~274MB)

### 第三步：启动 Morphik 主服务

```bash
# 启动 Morphik API 和 Worker
docker compose up --build -d morphik worker
```

### 第四步：验证部署

```bash
# 检查所有服务状态
docker compose ps

# 查看服务日志
docker compose logs morphik
docker compose logs ollama
```

## 🔍 服务访问

- **Morphik API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **Ollama API**: http://localhost:11434
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## 📁 数据存储

- **数据库数据**: Docker volume `postgres_data`
- **AI 模型**: Docker volume `ollama_data`
- **文档存储**: `./storage` 目录
- **日志文件**: `./logs` 目录
- **缓存数据**: Docker volume `redis_data`

## 🎮 使用示例

### 运行教程代码

```bash
# 进入 code 目录
cd code

# 运行交互式教程
python run_morphik_tutorial.py

# 或者单独运行各部分
python morphik_tutorial_part1.py  # 基础功能
python morphik_tutorial_part2.py  # 高级功能
python morphik_tutorial_part3.py  # LLM 配置与应用
```

### 基本 API 测试

```bash
# 健康检查
curl http://localhost:8000/health

# 查看 API 文档
open http://localhost:8000/docs
```

## 🔄 服务管理

### 启动所有服务
```bash
docker compose --profile ollama up -d
```

### 停止所有服务
```bash
docker compose down
```

### 重启特定服务
```bash
docker compose restart morphik
docker compose restart ollama
```

### 查看日志
```bash
# 查看所有服务日志
docker compose logs

# 查看特定服务日志
docker compose logs morphik
docker compose logs ollama
docker compose logs postgres
```

### 清理和重置
```bash
# 停止并删除所有容器和卷（会删除所有数据）
docker compose down -v

# 删除所有镜像
docker compose down --rmi all
```

## 🔧 故障排除

### 常见问题

#### 1. Ollama 模型下载失败
```bash
# 手动进入 Ollama 容器下载模型
docker compose exec ollama ollama pull qwen2.5:4b
docker compose exec ollama ollama pull nomic-embed-text:latest
```

#### 2. 内存不足
- 确保系统有足够的可用内存（至少 8GB）
- 考虑关闭其他占用内存的应用程序

#### 3. 磁盘空间不足
```bash
# 检查磁盘使用情况
df -h

# 清理 Docker 系统
docker system prune -a
```

#### 4. 端口冲突
如果端口被占用，可以修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8001:8000"  # 将 8000 改为 8001
```

### 性能优化

#### 1. 调整模型设备
编辑 `morphik.toml` 中的 reranker 配置：
```toml
[reranker]
device = "cpu"  # 在 Docker 中使用 CPU
```

#### 2. 调整数据库连接池
```toml
[database]
pool_size = 5           # 减少连接池大小
max_overflow = 10       # 减少最大溢出连接
```

## 🌐 切换为在线大模型

### 1. OpenAI 配置
编辑 `.env` 文件：
```bash
OPENAI_API_KEY=sk-your-openai-api-key-here
```

编辑 `morphik.toml`：
```toml
[completion]
model = "openai_gpt4o"

[embedding]
model = "openai_embedding"
```

### 2. Anthropic 配置
编辑 `.env` 文件：
```bash
ANTHROPIC_API_KEY=your-anthropic-api-key-here
```

编辑 `morphik.toml`：
```toml
[completion]
model = "claude_sonnet"
```

## 📊 监控和日志

### 查看系统资源使用
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
docker system df
```

### 日志管理
```bash
# 实时查看日志
docker compose logs -f morphik

# 查看最近的日志
docker compose logs --tail=100 morphik
```

## 🔐 安全建议

### 生产环境部署
1. **更改默认密钥**:
   ```bash
   # 生成新的 JWT 密钥
   openssl rand -hex 32
   ```

2. **使用 HTTPS**:
   - 配置反向代理（如 Nginx）
   - 使用 SSL 证书

3. **网络安全**:
   - 限制端口访问
   - 使用防火墙规则

4. **定期备份**:
   ```bash
   # 备份数据库
   docker compose exec postgres pg_dump -U morphik morphik > backup.sql
   
   # 备份存储目录
   tar -czf storage_backup.tar.gz storage/
   ```

## 📞 获取帮助

如果遇到问题，请：

1. **查看日志**: `docker compose logs`
2. **检查官方文档**: https://www.morphik.ai/docs/
3. **GitHub Issues**: https://github.com/morphik-org/morphik-core/issues
4. **社区支持**: Discord 社区

---

**部署完成！** 🎉 您现在可以开始使用 Morphik 进行文档处理和知识图谱构建了。
