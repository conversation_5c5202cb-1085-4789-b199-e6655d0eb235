# Morphik 项目结构说明

## 📁 项目目录结构

```
morphik-core_main/
├── 📄 manage_services.sh          # 服务管理脚本（主要工具）
├── 📄 local_start.py              # 本地启动脚本
├── 📄 simple_morphik_app.py       # 简化的 API 应用
├── 📄 docker-compose.yml          # Docker 服务配置
├── 📄 dockerfile                  # Docker 镜像构建文件
├── 📄 morphik.toml                # 应用配置文件
├── 📄 pyproject.toml              # Python 项目配置
├── 📄 uv.lock                     # 依赖锁定文件
├── 📄 fast_deploy.sh              # 快速部署脚本
├── 📄 quick_deploy.sh             # 快速部署脚本（备用）
├── 📄 .env                        # 环境变量配置（可选）
├── 📁 code/                       # 文档和教程目录
│   ├── 📄 Morphik服务启动教程.md   # 完整启动教程
│   ├── 📄 Morphik快速参考.md       # 快速参考卡片
│   └── 📄 项目结构说明.md          # 本文件
├── 📁 morphik/                    # 主要应用代码
│   ├── 📁 api/                    # API 相关代码
│   ├── 📁 core/                   # 核心功能
│   ├── 📁 models/                 # 数据模型
│   └── 📁 utils/                  # 工具函数
└── 📁 tests/                      # 测试文件
```

## 🔧 核心文件说明

### 服务管理文件

#### `manage_services.sh` ⭐ **主要工具**
- **功能**: 完整的服务管理脚本
- **用途**: 启动、停止、重启、监控所有服务
- **命令**: 
  - `./manage_services.sh start-all` - 启动所有服务
  - `./manage_services.sh stop` - 停止所有服务
  - `./manage_services.sh status` - 查看服务状态

#### `local_start.py`
- **功能**: 本地环境初始化脚本
- **用途**: 安装依赖、检查服务、创建简化应用
- **特点**: 自动处理 Python 环境和依赖安装

#### `simple_morphik_app.py`
- **功能**: 简化的 FastAPI 应用
- **用途**: 提供基本的 API 服务（健康检查、聊天、配置）
- **特点**: 轻量级，适合快速启动和测试

### 配置文件

#### `docker-compose.yml`
- **功能**: Docker 服务编排配置
- **服务**: PostgreSQL、Redis、Ollama
- **特点**: 包含健康检查、数据持久化、网络配置

#### `morphik.toml`
- **功能**: 应用主配置文件
- **内容**: 数据库、API、模型配置
- **格式**: TOML 格式，易于阅读和修改

#### `pyproject.toml`
- **功能**: Python 项目配置
- **内容**: 依赖管理、构建配置、项目元数据
- **工具**: 支持 uv、pip 等包管理器

### 部署脚本

#### `fast_deploy.sh`
- **功能**: 快速部署脚本
- **特点**: 自动化部署流程
- **状态**: 可能遇到网络问题，建议使用 `manage_services.sh`

#### `dockerfile`
- **功能**: Docker 镜像构建文件
- **用途**: 构建 Morphik 应用镜像
- **状态**: 简化版本，避免网络依赖问题

## 📚 文档文件

### `code/Morphik服务启动教程.md`
- **内容**: 完整的启动和使用教程
- **包含**: 安装、配置、故障排除、高级功能
- **适用**: 新用户和详细参考

### `code/Morphik快速参考.md`
- **内容**: 常用命令和快速参考
- **包含**: 一键启动、故障排除、常用操作
- **适用**: 日常使用和快速查询

### `code/项目结构说明.md`
- **内容**: 项目文件和目录结构说明
- **包含**: 文件功能、使用方法、重要性
- **适用**: 理解项目组织和文件作用

## 🚀 使用流程

### 1. 首次使用
```bash
# 1. 克隆项目
git clone <repository>
cd morphik-core_main

# 2. 给脚本执行权限
chmod +x manage_services.sh

# 3. 启动所有服务
./manage_services.sh start-all
```

### 2. 日常使用
```bash
# 启动服务
./manage_services.sh start-all

# 检查状态
./manage_services.sh status

# 停止服务
./manage_services.sh stop
```

### 3. 开发调试
```bash
# 启动基础服务
./manage_services.sh start-base

# 手动运行 API（开发模式）
python3 simple_morphik_app.py
```

## 🔍 文件重要性等级

### ⭐⭐⭐ 核心文件（必需）
- `manage_services.sh` - 主要管理工具
- `docker-compose.yml` - 服务配置
- `simple_morphik_app.py` - API 应用

### ⭐⭐ 重要文件（推荐）
- `morphik.toml` - 应用配置
- `local_start.py` - 环境初始化
- `code/Morphik服务启动教程.md` - 完整教程

### ⭐ 辅助文件（可选）
- `fast_deploy.sh` - 备用部署脚本
- `dockerfile` - 镜像构建
- `code/Morphik快速参考.md` - 快速参考

## 🛠️ 自定义配置

### 修改端口
编辑 `docker-compose.yml`:
```yaml
ports:
  - "5433:5432"  # PostgreSQL
  - "6379:6379"  # Redis
  - "11435:11434" # Ollama
```

### 修改模型
编辑 `morphik.toml`:
```toml
[completion]
model_name = "qwen2.5"  # 更改为其他模型

[embedding]
model_name = "nomic-embed-text:latest"  # 更改嵌入模型
```

### 添加环境变量
创建 `.env` 文件:
```bash
POSTGRES_PASSWORD=your_password
API_PORT=8000
OLLAMA_PORT=11435
```

## 📝 维护建议

1. **定期备份**: 使用教程中的备份脚本
2. **监控资源**: 定期检查 `docker stats`
3. **清理空间**: 定期运行 `docker system prune`
4. **更新模型**: 根据需要下载新模型
5. **查看日志**: 遇到问题时检查 `./manage_services.sh logs`

---

**提示**: 如果您是新用户，建议先阅读 `code/Morphik服务启动教程.md` 获取详细指导。
