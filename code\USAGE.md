# Morphik 统一管理脚本 - 快速使用指南

## 🎯 **成功合并完成！**

已成功将 `manage_services.sh` 和 `switch_model.sh` 的功能合并到 `code/manage_services.sh` 中。

## 📍 **使用方法**

**在项目根目录运行**（推荐）：
```bash
./code/manage_services.sh [命令]
```

**在 code 目录运行**（也支持）：
```bash
cd code
./manage_services.sh [命令]
```

## 🔧 **主要命令**

### **服务管理**
```bash
./code/manage_services.sh start              # 启动所有服务
./code/manage_services.sh stop               # 停止所有服务
./code/manage_services.sh restart            # 重启所有服务
./code/manage_services.sh status             # 查看服务状态
./code/manage_services.sh logs               # 查看日志
```

### **模型切换**
```bash
./code/manage_services.sh local              # 切换到本地模型
./code/manage_services.sh local restart      # 切换到本地模型并重启
./code/manage_services.sh online             # 切换到在线模型
./code/manage_services.sh online restart     # 切换到在线模型并重启
./code/manage_services.sh model-status       # 查看当前模型配置
```

### **帮助**
```bash
./code/manage_services.sh help               # 显示完整帮助
```

## 🔄 **模型配置对比**

| 配置项 | 本地模型 | 在线模型 |
|--------|----------|----------|
| **LLM** | ollama_qwen3_4b | qwen_max_online |
| **嵌入模型** | ollama_embedding_local | qwen_embedding_online |
| **向量维度** | 768维 | 1024维 |
| **成本** | 免费 | 需要API费用 |
| **性能** | 中等 | 高性能 |
| **网络要求** | 离线可用 | 需要网络连接 |

## ✅ **验证功能**

1. **查看当前配置**：
   ```bash
   ./code/manage_services.sh model-status
   ```

2. **测试模型切换**：
   ```bash
   # 切换到本地模型
   ./code/manage_services.sh local
   ./code/manage_services.sh model-status
   
   # 切换回在线模型
   ./code/manage_services.sh online
   ./code/manage_services.sh model-status
   ```

3. **测试服务管理**：
   ```bash
   ./code/manage_services.sh status
   ./code/manage_services.sh restart
   ```

## 🎉 **合并成果**

### **✅ 已实现的功能**
- ✅ 服务启动/停止/重启
- ✅ 服务状态检查
- ✅ 模型切换（本地 ↔ 在线）
- ✅ 向量维度自动调整（768 ↔ 1024）
- ✅ 配置文件自动备份
- ✅ 智能路径检测（支持根目录和code目录运行）
- ✅ 详细的状态显示
- ✅ 错误处理和用户提示

### **🔧 技术特性**
- **智能路径检测**：自动检测运行目录并调整配置文件路径
- **配置备份**：首次运行自动创建配置文件备份
- **维度同步**：模型切换时自动调整向量维度
- **服务集成**：一键切换模型并重启服务
- **状态监控**：详细的服务和模型状态显示

## 💡 **使用建议**

1. **开发阶段**：使用本地模型节省成本
   ```bash
   ./code/manage_services.sh local restart
   ```

2. **生产部署**：使用在线模型获得最佳性能
   ```bash
   ./code/manage_services.sh online restart
   ```

3. **日常维护**：定期检查服务状态
   ```bash
   ./code/manage_services.sh status
   ```

## 🗑️ **清理旧文件**

现在可以安全删除原来的独立脚本：
- `switch_model.sh` - 功能已合并到 `code/manage_services.sh`
- 原来的 `manage_services.sh` - 已被增强版本替代

**恭喜！Morphik 统一管理脚本已成功创建并测试完成！** 🎉
