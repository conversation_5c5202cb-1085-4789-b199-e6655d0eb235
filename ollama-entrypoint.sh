#!/bin/bash

echo "Starting Ollama server..."
ollama serve &
SERVE_PID=$!

echo "Waiting for Ollama server to be ready..."
while ! ollama list &>/dev/null; do
    echo "Waiting for Ollama to start..."
    sleep 1
done

echo "Pulling required models..."
ollama pull nomic-embed-text:latest || echo "Failed to pull nomic-embed-text:latest"
ollama pull qwen2.5:4b || echo "Failed to pull qwen2.5:4b"

# Wait for the serve process
wait $SERVE_PID
