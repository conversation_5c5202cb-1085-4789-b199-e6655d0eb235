#!/bin/bash

# Morphik 统一管理脚本
# 集成服务管理和智能模型切换功能
# 用于启动、停止、重启、查看状态和智能切换模型
# 包含自动向量维度管理功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_message $RED "❌ Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    print_message $BLUE "📊 服务状态检查"
    echo "=================================="

    # 检查 Docker 容器状态
    print_message $YELLOW "🐳 Docker 容器状态:"
    docker compose ps 2>/dev/null || echo "没有运行的容器"

    echo ""

    # 检查端口占用
    print_message $YELLOW "🔌 端口占用情况:"
    echo "PostgreSQL (5433): $(lsof -i :5433 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"
    echo "Redis (6379):      $(lsof -i :6379 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"
    echo "Morphik API (8000): $(lsof -i :8000 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"
    echo "Ollama (11434):    $(lsof -i :11434 >/dev/null 2>&1 && echo '✅ 占用' || echo '❌ 空闲')"

    echo ""

    # 检查 API 健康状态
    print_message $YELLOW "🏥 API 健康检查:"

    # 检查完整的 Morphik API
    if curl -s http://localhost:8000/ping >/dev/null 2>&1; then
        echo "✅ 完整的 Morphik API 正常运行"

        # 检查具体功能端点
        echo "📋 功能端点检查:"
        if curl -s http://localhost:8000/ingest/text -X POST -H "Content-Type: application/json" -d '{}' >/dev/null 2>&1; then
            echo "  ✅ 文档摄取端点可用"
        else
            echo "  ❌ 文档摄取端点不可用"
        fi

        if curl -s http://localhost:8000/query -X POST -H "Content-Type: application/json" -d '{}' >/dev/null 2>&1; then
            echo "  ✅ 查询端点可用"
        else
            echo "  ❌ 查询端点不可用"
        fi

    elif curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "⚠️  简化版 Morphik API 正在运行（功能有限）"
    else
        echo "❌ Morphik API 未响应"
    fi

    # 检查 Ollama 服务
    if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
        echo "✅ Ollama 服务正常运行"

        # 检查已安装的模型
        models=$(curl -s http://localhost:11434/api/tags | python3 -c "import json, sys; data=json.load(sys.stdin); print(', '.join([m['name'] for m in data.get('models', [])]))" 2>/dev/null)
        if [ ! -z "$models" ]; then
            echo "  📦 已安装模型: $models"
        fi
    else
        echo "❌ Ollama 服务未响应"
    fi
}

# 停止所有服务
stop_services() {
    print_message $YELLOW "🛑 停止所有服务..."

    # 停止完整的 Morphik API 进程
    print_message $BLUE "停止 Morphik API 进程..."
    if [ -f ".morphik_pid" ]; then
        MORPHIK_PID=$(cat .morphik_pid | grep "Morphik API PID:" | cut -d: -f2 | tr -d ' ')
        if [ ! -z "$MORPHIK_PID" ]; then
            print_message $BLUE "停止 Morphik API (PID: $MORPHIK_PID)..."
            kill $MORPHIK_PID 2>/dev/null || echo "进程可能已经停止"
            sleep 2
            # 如果进程仍在运行，强制杀死
            if kill -0 $MORPHIK_PID 2>/dev/null; then
                kill -9 $MORPHIK_PID 2>/dev/null || echo "强制停止进程"
            fi
        fi
        rm -f .morphik_pid
    fi

    # 停止其他可能运行的 Python 进程 - 更强力的停止方法
    print_message $BLUE "停止其他 Python API 进程..."

    # 停止所有 Morphik 相关进程
    pkill -9 -f "start_full_morphik" 2>/dev/null || echo "没有运行的完整 Morphik 进程"
    pkill -9 -f "simple_morphik_app" 2>/dev/null || echo "没有运行的简化 Morphik 进程"
    pkill -9 -f "local_start" 2>/dev/null || echo "没有运行的本地启动进程"

    # 停止所有 uvicorn 和 API 进程
    pkill -9 -f "uvicorn.*morphik" 2>/dev/null || echo "没有运行的 Uvicorn 进程"
    pkill -9 -f "uvicorn.*core.api" 2>/dev/null || echo "没有运行的核心 API 进程"
    pkill -9 -f "core.api:app" 2>/dev/null || echo "没有运行的核心 API 进程"
    pkill -9 -f "python.*core.api" 2>/dev/null || echo "没有运行的 Python API 进程"

    # 停止端口 8000 上的所有进程
    lsof -ti:8000 | xargs kill -9 2>/dev/null || echo "端口 8000 上没有运行的进程"

    # 停止 ARQ worker 进程
    print_message $BLUE "停止 ARQ worker 进程..."
    pkill -9 -f "arq.*worker" 2>/dev/null || echo "没有运行的 ARQ worker 进程"
    pkill -9 -f "ingestion_worker" 2>/dev/null || echo "没有运行的摄取 worker 进程"

    # 停止 Docker 容器
    print_message $BLUE "停止 Docker 容器..."
    docker compose down 2>/dev/null || echo "没有运行的容器需要停止"

    # 等待端口释放
    sleep 5

    print_message $GREEN "✅ 所有服务已停止"
}

# 检查 morphik.toml 中的 ColPali 配置
check_colpali_config() {
    if [ -n "$MORPHIK_CONFIG" ] && [ -f "$MORPHIK_CONFIG" ]; then
        # 使用 grep 和 awk 来解析 TOML 文件中的 enable_colpali 配置，去除注释
        COLPALI_ENABLED=$(grep -E "^\s*enable_colpali\s*=" "$MORPHIK_CONFIG" | awk -F'=' '{print $2}' | awk -F'#' '{print $1}' | tr -d ' ' | tr -d '"' | tr -d "'")
        if [ "$COLPALI_ENABLED" = "true" ]; then
            return 0  # ColPali 启用
        else
            return 1  # ColPali 禁用
        fi
    else
        print_message $YELLOW "⚠️  未找到 morphik.toml 配置文件，默认禁用 ColPali"
        return 1  # 默认禁用
    fi
}

# 启动基础服务（PostgreSQL, Redis）
start_base_services() {
    print_message $YELLOW "🚀 启动基础服务..."

    check_docker

    # 启动基础服务
    print_message $BLUE "启动 PostgreSQL, Redis..."
    docker compose up -d postgres redis

    # 等待服务启动
    print_message $BLUE "等待服务启动..."
    sleep 10

    # 检查 Docker 服务状态
    if docker compose ps | grep -q "Up"; then
        print_message $GREEN "✅ Docker 基础服务启动成功"
    else
        print_message $RED "❌ Docker 基础服务启动失败"
        exit 1
    fi

    # 检查本机 Ollama 服务
    print_message $BLUE "检查本机 Ollama 服务..."
    if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
        print_message $GREEN "✅ 本机 Ollama 服务正常运行"
    else
        print_message $YELLOW "⚠️  本机 Ollama 服务未运行，请手动启动:"
        print_message $BLUE "   运行命令: ollama serve"
        print_message $BLUE "   或在新终端中运行: ollama serve &"
    fi
}

# 统一的 Morphik API 启动函数（根据配置决定是否启用 ColPali）
start_morphik_api() {
    # 检查 ColPali 配置
    if check_colpali_config; then
        print_message $YELLOW "🚀 启动完整的 Morphik API (包含 ColPali 多模态功能)..."
        COLPALI_STATUS="启用"
        WAIT_TIME=60
        COLPALI_ENV=""
    else
        print_message $YELLOW "🚀 启动完整的 Morphik API (ColPali 已禁用)..."
        COLPALI_STATUS="禁用"
        WAIT_TIME=30
        COLPALI_ENV="DISABLE_COLPALI=1"
    fi

    # 检查基础服务是否运行
    if ! docker compose ps | grep -q "Up"; then
        print_message $YELLOW "基础服务未运行，先启动基础服务..."
        start_base_services
    fi

    # 启动完整的 Morphik API
    print_message $BLUE "启动完整的 Morphik API 服务 (ColPali: $COLPALI_STATUS)..."
    if [ -f "start_full_morphik.py" ]; then
        # 确保 logs 目录存在
        mkdir -p logs

        # 根据配置启动服务
        if [ "$COLPALI_STATUS" = "启用" ]; then
            # 传递环境变量给 Python 进程
            env MORPHIK_AUTO_CONFIRM="${MORPHIK_AUTO_CONFIRM:-no}" python3 start_full_morphik.py --host 0.0.0.0 --port 8000 > logs/morphik_api.log 2>&1 &
            print_message $BLUE "等待 Morphik API 启动 (包括 ColPali 模型加载)..."
            print_message $BLUE "这可能需要 60-120 秒，请耐心等待..."
            print_message $YELLOW "⚠️  ColPali 模型较大，首次启动会下载模型文件"
        else
            # 传递环境变量给 Python 进程
            env DISABLE_COLPALI=1 MORPHIK_AUTO_CONFIRM="${MORPHIK_AUTO_CONFIRM:-no}" python3 start_full_morphik.py --host 0.0.0.0 --port 8000 > logs/morphik_api.log 2>&1 &
            print_message $BLUE "等待 Morphik API 启动（包括模型加载）..."
            print_message $BLUE "这可能需要 30-60 秒，请耐心等待..."
        fi

        MORPHIK_PID=$!
        sleep $WAIT_TIME

        # 检查服务是否启动成功
        if curl -s http://localhost:8000/ping >/dev/null 2>&1; then
            if [ "$COLPALI_STATUS" = "启用" ]; then
                print_message $GREEN "✅ 完整的 Morphik API (含 ColPali) 启动成功"
            else
                print_message $GREEN "✅ 完整的 Morphik API 启动成功"
            fi
            print_message $BLUE "🌐 访问地址:"
            echo "   - API: http://localhost:8000"
            echo "   - 文档: http://localhost:8000/docs"
            echo "   - 健康检查: http://localhost:8000/ping"
            echo "   - 文档摄取: http://localhost:8000/ingest/text"
            echo "   - 文档查询: http://localhost:8000/query"
            if [ "$COLPALI_STATUS" = "启用" ]; then
                echo "   - 多模态摄取: http://localhost:8000/ingest/file (支持图像)"
            fi
            print_message $BLUE "📋 日志文件: logs/morphik_api.log"
            echo "Morphik API PID: $MORPHIK_PID" > .morphik_pid
        else
            print_message $RED "❌ Morphik API 启动失败"
            print_message $YELLOW "请检查日志文件: logs/morphik_api.log"
            if [ "$COLPALI_STATUS" = "启用" ]; then
                print_message $YELLOW "ColPali 模型可能仍在加载中，请等待更长时间"
            fi
        fi
    else
        print_message $RED "❌ 找不到 start_full_morphik.py 文件"
        print_message $YELLOW "回退到简化版 API..."

        # 回退到简化版本
        if [ -f "simple_morphik_app.py" ]; then
            python3 simple_morphik_app.py &
            sleep 3

            if curl -s http://localhost:8000/health >/dev/null 2>&1; then
                print_message $GREEN "✅ 简化版 Morphik API 启动成功"
                print_message $BLUE "🌐 访问地址:"
                echo "   - API: http://localhost:8000"
                echo "   - 文档: http://localhost:8000/docs"
                echo "   - 健康检查: http://localhost:8000/health"
            else
                print_message $RED "❌ API 启动失败"
            fi
        fi
    fi
}

# 启动所有服务（根据配置决定是否启用 ColPali）
start_all_services() {
    print_message $YELLOW "🚀 启动所有服务..."
    start_base_services
    start_morphik_api
}

# 重启所有服务
restart_services() {
    print_message $YELLOW "🔄 重启所有服务..."
    stop_services
    sleep 3
    start_all_services
}

# 显示日志
show_logs() {
    print_message $BLUE "📋 显示服务日志"
    echo "=================================="

    print_message $YELLOW "Docker 容器日志:"
    docker compose logs --tail=50 2>/dev/null || echo "没有容器日志"
}

# 配置文件路径
MORPHIK_CONFIG="morphik.toml"
BACKUP_CONFIG="morphik.toml.backup"

# 检测运行目录并调整配置文件路径
if [ -f "morphik.toml" ]; then
    # 在项目根目录运行
    MORPHIK_CONFIG="morphik.toml"
    BACKUP_CONFIG="morphik.toml.backup"
elif [ -f "../morphik.toml" ]; then
    # 在 code 目录运行
    MORPHIK_CONFIG="../morphik.toml"
    BACKUP_CONFIG="../morphik.toml.backup"
else
    # 找不到配置文件
    MORPHIK_CONFIG=""
    BACKUP_CONFIG=""
fi

# 备份配置文件
backup_config() {
    if [ ! -f "$BACKUP_CONFIG" ]; then
        print_message $YELLOW "创建配置文件备份..."
        cp "$MORPHIK_CONFIG" "$BACKUP_CONFIG"
    fi
}

# 检查 Python 环境和依赖
check_python_deps() {
    # 检查 Python3 是否可用
    if ! command -v python3 >/dev/null 2>&1; then
        print_message $RED "❌ 未找到 python3，智能切换功能需要 Python3"
        return 1
    fi

    # 检查 asyncpg 是否安装
    if ! python3 -c "import asyncpg" >/dev/null 2>&1; then
        print_message $YELLOW "⚠️  未安装 asyncpg，尝试安装..."
        pip3 install asyncpg >/dev/null 2>&1 || {
            print_message $RED "❌ 无法安装 asyncpg，请手动安装: pip3 install asyncpg"
            return 1
        }
    fi

    return 0
}

# 智能切换到本地模型（包含向量维度管理）
smart_switch_to_local() {
    print_message $GREEN "🎯 智能切换到本地 Ollama 模型..."
    print_message $BLUE "💡 此功能将自动处理配置文件、数据库维度和服务重启"
    echo ""

    # 检查配置文件是否存在
    if [ -z "$MORPHIK_CONFIG" ] || [ ! -f "$MORPHIK_CONFIG" ]; then
        print_message $RED "错误: 找不到 morphik.toml 配置文件"
        print_message $YELLOW "请确保在项目根目录运行此脚本，或者配置文件存在"
        return 1
    fi

    # 检查 Python 依赖
    if ! check_python_deps; then
        print_message $YELLOW "回退到基础切换模式..."
        switch_to_local
        return $?
    fi

    backup_config

    # 1. 更新配置文件
    print_message $YELLOW "⚙️  更新配置文件..."
    sed -i.tmp '/^\[completion\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^model = "qwen_embedding_online"/model = "ollama_embedding_local"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 1024/dimensions = 768/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 1536/dimensions = 768/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[agent\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[document_analysis\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[parser\.vision\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[rules\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[graph\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    rm -f "$MORPHIK_CONFIG.tmp"
    print_message $GREEN "✅ 配置文件更新完成"

    # 2. 确保向量表维度正确
    print_message $YELLOW "🔧 确保向量表维度为 768 维..."

    # 检查向量维度管理器的位置
    VECTOR_MANAGER=""
    if [ -f "code/vector_dimension_manager.py" ]; then
        VECTOR_MANAGER="code/vector_dimension_manager.py"
    elif [ -f "vector_dimension_manager.py" ]; then
        VECTOR_MANAGER="vector_dimension_manager.py"
    elif [ -f "../vector_dimension_manager.py" ]; then
        VECTOR_MANAGER="../vector_dimension_manager.py"
    fi

    if [ -n "$VECTOR_MANAGER" ]; then
        if python3 "$VECTOR_MANAGER" ensure 768; then
            print_message $GREEN "✅ 向量表维度设置完成"
        else
            print_message $RED "❌ 向量表维度设置失败"
            return 1
        fi
    else
        print_message $YELLOW "⚠️  未找到向量维度管理器，跳过维度设置"
    fi

    print_message $GREEN "✅ 智能切换到本地模型配置完成"
    echo "  - LLM: ollama_qwen3_4b (Qwen3 4B)"
    echo "  - Embedding: ollama_embedding_local (Nomic Embed, 768维)"
    echo "  - 向量表: 768维"
}

# 切换到本地模型（基础版本，保持兼容性）
switch_to_local() {
    print_message $GREEN "切换到本地 Ollama 模型..."

    # 检查配置文件是否存在
    if [ -z "$MORPHIK_CONFIG" ] || [ ! -f "$MORPHIK_CONFIG" ]; then
        print_message $RED "错误: 找不到 morphik.toml 配置文件"
        print_message $YELLOW "请确保在项目根目录运行此脚本，或者配置文件存在"
        return 1
    fi

    backup_config

    # 更新所有组件的模型配置
    sed -i.tmp '/^\[completion\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^model = "qwen_embedding_online"/model = "ollama_embedding_local"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 1024/dimensions = 768/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 1536/dimensions = 768/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[agent\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[document_analysis\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[parser\.vision\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[rules\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[graph\]/,/^\[/ s/^model = "qwen_.*_online"/model = "ollama_qwen3_4b"/' "$MORPHIK_CONFIG"

    # 清理临时文件
    rm -f "$MORPHIK_CONFIG.tmp"

    print_message $GREEN "✓ 已切换到本地模型配置"
    echo "  - LLM: ollama_qwen3_4b (Qwen3 4B)"
    echo "  - Embedding: ollama_embedding_local (Nomic Embed, 768维)"
}

# 智能切换到在线模型（包含向量维度管理）
smart_switch_to_online() {
    print_message $GREEN "🎯 智能切换到在线通义千问模型..."
    print_message $BLUE "💡 此功能将自动处理配置文件、数据库维度和服务重启"
    echo ""

    # 检查配置文件是否存在
    if [ -z "$MORPHIK_CONFIG" ] || [ ! -f "$MORPHIK_CONFIG" ]; then
        print_message $RED "错误: 找不到 morphik.toml 配置文件"
        print_message $YELLOW "请确保在项目根目录运行此脚本，或者配置文件存在"
        return 1
    fi

    # 检查 Python 依赖
    if ! check_python_deps; then
        print_message $YELLOW "回退到基础切换模式..."
        switch_to_online
        return $?
    fi

    backup_config

    # 1. 更新配置文件
    print_message $YELLOW "⚙️  更新配置文件..."
    sed -i.tmp '/^\[completion\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^model = "ollama_embedding_local"/model = "qwen_embedding_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 768/dimensions = 1024/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[agent\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[document_analysis\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[parser\.vision\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[rules\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[graph\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    rm -f "$MORPHIK_CONFIG.tmp"
    print_message $GREEN "✅ 配置文件更新完成"

    # 2. 确保向量表维度正确
    print_message $YELLOW "🔧 确保向量表维度为 1024 维..."

    # 检查向量维度管理器的位置
    VECTOR_MANAGER=""
    if [ -f "code/vector_dimension_manager.py" ]; then
        VECTOR_MANAGER="code/vector_dimension_manager.py"
    elif [ -f "vector_dimension_manager.py" ]; then
        VECTOR_MANAGER="vector_dimension_manager.py"
    elif [ -f "../vector_dimension_manager.py" ]; then
        VECTOR_MANAGER="../vector_dimension_manager.py"
    fi

    if [ -n "$VECTOR_MANAGER" ]; then
        if python3 "$VECTOR_MANAGER" ensure 1024; then
            print_message $GREEN "✅ 向量表维度设置完成"
        else
            print_message $RED "❌ 向量表维度设置失败"
            return 1
        fi
    else
        print_message $YELLOW "⚠️  未找到向量维度管理器，跳过维度设置"
    fi

    print_message $GREEN "✅ 智能切换到在线模型配置完成"
    echo "  - LLM: qwen_max_online (通义千问 Max)"
    echo "  - Embedding: qwen_embedding_online (Text Embedding V3, 1024维)"
    echo "  - 向量表: 1024维"
}

# 切换到在线模型（基础版本，保持兼容性）
switch_to_online() {
    print_message $GREEN "切换到在线通义千问模型..."

    # 检查配置文件是否存在
    if [ -z "$MORPHIK_CONFIG" ] || [ ! -f "$MORPHIK_CONFIG" ]; then
        print_message $RED "错误: 找不到 morphik.toml 配置文件"
        print_message $YELLOW "请确保在项目根目录运行此脚本，或者配置文件存在"
        return 1
    fi

    backup_config

    # 更新所有组件的模型配置
    sed -i.tmp '/^\[completion\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^model = "ollama_embedding_local"/model = "qwen_embedding_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[embedding\]/,/^\[/ s/^dimensions = 768/dimensions = 1024/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[agent\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[document_analysis\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[parser\.vision\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[rules\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"
    sed -i.tmp '/^\[graph\]/,/^\[/ s/^model = "ollama_qwen3_4b"/model = "qwen_max_online"/' "$MORPHIK_CONFIG"

    # 清理临时文件
    rm -f "$MORPHIK_CONFIG.tmp"

    print_message $GREEN "✓ 已切换到在线模型配置"
    echo "  - LLM: qwen_max_online (通义千问 Max)"
    echo "  - Embedding: qwen_embedding_online (Text Embedding V3, 1024维)"
}

# 显示当前模型配置状态
show_model_status() {
    print_message $BLUE "📊 当前模型配置状态:"
    echo ""

    if [ -z "$MORPHIK_CONFIG" ] || [ ! -f "$MORPHIK_CONFIG" ]; then
        print_message $RED "错误: 找不到 morphik.toml 配置文件"
        print_message $YELLOW "请确保在项目根目录运行此脚本，或者配置文件存在"
        return 1
    fi

    # 检查 completion 模型
    completion_model=$(grep -E "^\[completion\]" -A 5 "$MORPHIK_CONFIG" | grep "^model" | cut -d'"' -f2)
    echo "LLM 模型: $completion_model"

    # 检查 embedding 模型
    embedding_model=$(grep -E "^\[embedding\]" -A 5 "$MORPHIK_CONFIG" | grep "^model" | cut -d'"' -f2)
    echo "嵌入模型: $embedding_model"

    # 检查维度设置
    dimensions=$(grep -E "^\[embedding\]" -A 5 "$MORPHIK_CONFIG" | grep "^dimensions" | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' ')
    echo "向量维度: $dimensions"

    echo ""
    if [[ "$completion_model" == *"ollama"* ]]; then
        print_message $GREEN "当前使用: 本地模型"
    elif [[ "$completion_model" == *"qwen"* ]]; then
        print_message $GREEN "当前使用: 在线模型"
    else
        print_message $YELLOW "当前使用: 其他模型"
    fi
}

# 显示帮助信息
show_help() {
    echo "Morphik 统一管理脚本"
    echo "===================="
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "🔧 服务管理命令:"
    echo "  start           启动所有服务 (基础服务 + Morphik API)"
    echo "  stop            停止所有服务 (包括 Docker 容器和 Python 进程)"
    echo "  restart         重启所有服务"
    echo "  status          显示详细的服务状态和功能检查"
    echo "  logs            显示服务日志"
    echo ""
    echo "🎯 智能模型切换命令 (推荐):"
    echo "  smart-local     智能切换到本地模型 (自动处理配置+数据库+重启)"
    echo "  smart-online    智能切换到在线模型 (自动处理配置+数据库+重启)"
    echo ""
    echo "🔄 基础模型切换命令:"
    echo "  local [restart] 切换到本地 Ollama 模型 (仅更新配置)"
    echo "  online [restart] 切换到在线通义千问模型 (仅更新配置)"
    echo "  model-status    显示当前模型配置"
    echo ""
    echo "📋 其他命令:"
    echo "  help            显示此帮助信息"
    echo ""
    echo "🎯 智能切换 vs 基础切换:"
    echo "  智能切换: 自动处理配置文件、向量表维度、服务重启，一键完成"
    echo "  基础切换: 仅更新配置文件，需要手动处理数据库和重启"
    echo ""
    echo "ColPali 多模态功能:"
    echo "  ColPali 功能的启用/禁用由 morphik.toml 配置文件中的 enable_colpali 设置决定"
    echo "  - enable_colpali = true   启用 ColPali 多模态功能 (支持图像处理)"
    echo "  - enable_colpali = false  禁用 ColPali 功能 (仅支持文本)"
    echo ""
    echo "示例:"
    echo "  $0 start              # 启动所有服务"
    echo "  $0 stop               # 停止所有服务"
    echo "  $0 status             # 查看服务状态"
    echo "  $0 restart            # 重启所有服务"
    echo "  $0 smart-local        # 智能切换到本地模型 (推荐)"
    echo "  $0 smart-online       # 智能切换到在线模型 (推荐)"
    echo "  $0 local              # 基础切换到本地模型"
    echo "  $0 online restart     # 基础切换到在线模型并重启服务"
    echo "  $0 model-status       # 查看当前模型配置"
}

# 主逻辑
case "${1:-help}" in
    "start")
        start_all_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    # 智能模型切换命令 (推荐)
    "smart-local")
        smart_switch_to_local
        print_message $YELLOW "🔄 重启服务以应用新配置..."
        # 设置环境变量确保自动确认
        export MORPHIK_AUTO_CONFIRM=yes
        restart_services
        unset MORPHIK_AUTO_CONFIRM
        print_message $GREEN "🎉 智能切换到本地模型完成！"
        print_message $BLUE "🧪 测试命令: cd code && python 1.py"
        ;;
    "smart-online")
        smart_switch_to_online
        print_message $YELLOW "🔄 重启服务以应用新配置..."
        # 设置环境变量确保自动确认
        export MORPHIK_AUTO_CONFIRM=yes
        restart_services
        unset MORPHIK_AUTO_CONFIRM
        print_message $GREEN "🎉 智能切换到在线模型完成！"
        print_message $BLUE "🧪 测试命令: cd code && python 1.py"
        ;;
    # 基础模型切换命令 (保持兼容性)
    "local")
        switch_to_local
        if [ "$2" = "restart" ]; then
            print_message $YELLOW "重启服务以应用新配置..."
            restart_services
        else
            print_message $YELLOW "提示: 配置已更新，需要重启服务才能生效"
            print_message $BLUE "运行以下命令重启服务:"
            echo "  $0 local restart"
            echo "或手动重启:"
            echo "  $0 restart"
            print_message $YELLOW "💡 建议使用智能切换: $0 smart-local"
        fi
        ;;
    "online")
        switch_to_online
        if [ "$2" = "restart" ]; then
            print_message $YELLOW "重启服务以应用新配置..."
            restart_services
        else
            print_message $YELLOW "提示: 配置已更新，需要重启服务才能生效"
            print_message $BLUE "运行以下命令重启服务:"
            echo "  $0 online restart"
            echo "或手动重启:"
            echo "  $0 restart"
            print_message $YELLOW "💡 建议使用智能切换: $0 smart-online"
        fi
        ;;
    "model-status")
        show_model_status
        ;;
    # 保留旧命令以兼容性（但不在帮助中显示）
    "start-base")
        start_base_services
        ;;
    "start-api")
        start_morphik_api
        ;;
    "start-all")
        start_all_services
        ;;
    "help"|*)
        show_help
        ;;
esac
