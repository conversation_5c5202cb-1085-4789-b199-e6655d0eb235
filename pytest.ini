[pytest]
# Async settings
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Test file patterns
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test paths - ensure this points to your test directory relative to pytest.ini location
testpaths = tests

# Markers
markers =
    integration: marks tests as integration tests
    api: marks tests as API tests
    sdk: marks tests as SDK tests
    unit: marks tests as unit tests

# Output settings
addopts =
    -v
    -s
    --showlocals
    --tb=short

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Python path - ensure core and sdk are in path
pythonpath =
    .
    ../sdks/python

# Directories to ignore
norecursedirs =
    .*
    build
    dist
    *.egg
    venv
    env
    .venv
