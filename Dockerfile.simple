# 简化的 Dockerfile，减少网络依赖
FROM python:3.11.12-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 只安装最基本的依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安装 pip 和基础 Python 包
RUN pip install --no-cache-dir --upgrade pip

# 复制 requirements 文件
COPY requirements.txt .

# 安装 Python 依赖（分批安装以减少网络压力）
RUN pip install --no-cache-dir fastapi uvicorn python-dotenv

# 安装核心依赖
RUN pip install --no-cache-dir \
    sqlalchemy \
    alembic \
    psycopg2-binary \
    redis \
    pydantic

# 安装 AI/ML 依赖
RUN pip install --no-cache-dir \
    openai \
    litellm \
    instructor

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/logs /app/storage

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "uvicorn", "core.api:app", "--host", "0.0.0.0", "--port", "8000"]
