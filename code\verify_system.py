#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Morphik 系统验证脚本
快速验证模型切换系统是否正常工作
"""

import subprocess
import time
import sys
import os
from morphik import Morphik

def check_service_status():
    """检查服务状态"""
    print("🔍 检查服务状态...")
    try:
        result = subprocess.run(
            ["./manage_services.sh", "status"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if "✅ 完整的 Morphik API 正常运行" in result.stdout:
            print("✅ Morphik API 服务正常")
            return True
        else:
            print("❌ Morphik API 服务异常")
            return False
            
    except Exception as e:
        print(f"❌ 检查服务状态失败: {e}")
        return False

def check_model_config():
    """检查当前模型配置"""
    print("⚙️  检查模型配置...")
    try:
        result = subprocess.run(
            ["./manage_services.sh", "model-status"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if "LLM 模型:" in line or "嵌入模型:" in line or "向量维度:" in line or "当前使用:" in line:
                    print(f"   {line}")
            return True
        else:
            print("❌ 无法获取模型配置")
            return False
            
    except Exception as e:
        print(f"❌ 检查模型配置失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    try:
        # 连接测试
        db = Morphik(is_local=True, timeout=30)
        print("   ✅ 连接成功")
        
        # 摄取测试
        test_content = "这是一个系统验证测试文档。"
        doc = db.ingest_text(
            content=test_content,
            filename="system_verify_test.txt",
            metadata={"测试": "系统验证", "时间": time.strftime("%Y-%m-%d %H:%M:%S")}
        )
        print(f"   ✅ 文档摄取成功: {doc.external_id}")
        
        # 等待索引
        time.sleep(2)
        
        # 查询测试
        response = db.query(
            query="这是什么文档？",
            k=1,
            temperature=0.5
        )
        print(f"   ✅ 查询成功，回答长度: {len(response.completion)}字符")
        
        db.close()
        print("   ✅ 连接关闭")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 功能测试失败: {e}")
        return False

def verify_switching_tools():
    """验证切换工具"""
    print("🛠️  验证切换工具...")
    
    tools = [
        "smart_model_switch.py",
        "fix_768_dimensions.py", 
        "fix_1024_dimensions.py",
        "check_table_structure.py"
    ]
    
    all_exist = True
    for tool in tools:
        if os.path.exists(tool):
            print(f"   ✅ {tool}")
        else:
            print(f"   ❌ {tool} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主验证流程"""
    print("🎯 Morphik 系统验证")
    print("=" * 50)
    print("💡 此脚本将验证模型切换系统是否正常工作")
    print()
    
    # 检查当前目录
    if not os.path.exists("../manage_services.sh"):
        print("❌ 请在 morphik-Demo/code 目录下运行此脚本")
        sys.exit(1)
    
    # 切换到正确目录
    os.chdir("..")
    
    results = []
    
    # 1. 检查服务状态
    results.append(("服务状态", check_service_status()))
    
    # 2. 检查模型配置
    results.append(("模型配置", check_model_config()))
    
    # 3. 验证切换工具
    os.chdir("code")
    results.append(("切换工具", verify_switching_tools()))
    
    # 4. 测试基本功能
    results.append(("基本功能", test_basic_functionality()))
    
    # 输出验证结果
    print("\n" + "=" * 50)
    print("📊 验证结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 系统验证完全通过！")
        print()
        print("✅ 所有功能正常工作")
        print("✅ 模型切换系统可用")
        print("✅ 可以开始使用 Morphik")
        print()
        print("🚀 快速开始:")
        print("   # 切换到本地模型")
        print("   python smart_model_switch.py local")
        print()
        print("   # 切换到在线模型") 
        print("   python smart_model_switch.py online")
        print()
        print("   # 运行完整测试")
        print("   python 1.py")
        
    else:
        print("⚠️  系统验证发现问题！")
        print()
        print("🔧 建议的修复步骤:")
        print("1. 检查服务是否正常启动:")
        print("   cd .. && ./manage_services.sh restart")
        print()
        print("2. 检查依赖是否安装:")
        print("   pip install morphik")
        print()
        print("3. 检查 Docker 服务:")
        print("   docker-compose ps")
        print()
        print("4. 查看详细日志:")
        print("   tail -f ../logs/morphik_api.log")
        
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  验证被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 验证过程出错: {e}")
        sys.exit(1)
