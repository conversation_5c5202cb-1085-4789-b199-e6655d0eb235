#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查数据库表结构
查看向量表的实际维度设置
"""

import asyncio
import asyncpg

async def check_table_structure():
    """检查数据库表结构"""
    print("🔍 检查数据库表结构...")
    
    try:
        # 连接到数据库
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 检查向量表是否存在
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%vector%'
            ORDER BY table_name;
        """)
        
        print(f"\n📊 找到 {len(tables)} 个向量相关表:")
        for table in tables:
            print(f"   - {table['table_name']}")
        
        # 检查 vector_embeddings 表结构
        if any(table['table_name'] == 'vector_embeddings' for table in tables):
            print(f"\n🔍 检查 vector_embeddings 表结构:")
            
            # 获取表结构
            columns = await conn.fetch("""
                SELECT column_name, data_type, character_maximum_length
                FROM information_schema.columns 
                WHERE table_name = 'vector_embeddings' 
                AND table_schema = 'public'
                ORDER BY ordinal_position;
            """)
            
            for col in columns:
                print(f"   - {col['column_name']}: {col['data_type']}")
                if col['column_name'] == 'embedding':
                    # 获取向量维度
                    vector_info = await conn.fetchrow("""
                        SELECT atttypmod 
                        FROM pg_attribute 
                        WHERE attrelid = 'vector_embeddings'::regclass 
                        AND attname = 'embedding';
                    """)
                    if vector_info and vector_info['atttypmod'] != -1:
                        dimensions = vector_info['atttypmod']
                        print(f"     📏 向量维度: {dimensions}")
                    else:
                        print(f"     📏 向量维度: 未指定或动态")
        
        # 检查元数据表中的维度设置
        print(f"\n📝 检查元数据表中的维度设置:")
        metadata = await conn.fetch("""
            SELECT key, value 
            FROM vector_store_metadata 
            WHERE key = 'dimensions'
            ORDER BY key;
        """)
        
        if metadata:
            for meta in metadata:
                print(f"   - {meta['key']}: {meta['value']}")
        else:
            print("   - 未找到维度设置")
        
        # 尝试获取实际的向量维度（通过查询现有数据）
        print(f"\n🔍 检查实际存储的向量维度:")
        try:
            sample = await conn.fetchrow("""
                SELECT array_length(embedding, 1) as actual_dimensions
                FROM vector_embeddings 
                LIMIT 1;
            """)
            if sample:
                print(f"   - 实际存储的向量维度: {sample['actual_dimensions']}")
            else:
                print("   - 表中暂无数据")
        except Exception as e:
            print(f"   - 无法获取实际维度: {e}")
        
        await conn.close()
        
        print("\n🎉 表结构检查完成！")
        return True
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        return False

async def main():
    """主函数"""
    print("Morphik 数据库表结构检查工具")
    print("=" * 40)
    
    await check_table_structure()

if __name__ == "__main__":
    asyncio.run(main())
