#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
知识图谱功能测试 - 使用扩展超时时间
专门测试知识图谱的构建和查询功能
"""

import time
from morphik import Morphik

def test_knowledge_graph_with_extended_timeout():
    """测试知识图谱功能，使用扩展的超时时间"""
    print("🧪 测试知识图谱功能（扩展超时时间）")
    print("=" * 50)

    try:
        # 使用更长的超时时间连接（5分钟）
        print("🔗 连接到 Morphik（超时时间：300秒）...")
        db = Morphik(is_local=True, timeout=300)
        print("✅ 连接成功")

        # 1. 摄取一些测试文档用于构建知识图谱
        print("\n📝 摄取测试文档...")

        # 文档1：关于人工智能的文档
        doc1 = db.ingest_text(
            content="""
            人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。
            机器学习是人工智能的一个子领域，它使计算机能够从数据中学习而无需明确编程。
            深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。
            自然语言处理（NLP）是人工智能的另一个重要领域，专注于计算机与人类语言之间的交互。
            """,
            filename="ai_overview.txt",
            metadata={"类别": "技术", "主题": "人工智能"}
        )
        print(f"✅ 文档1摄取成功，ID: {doc1.external_id}")

        # 文档2：关于技术应用的文档
        doc2 = db.ingest_text(
            content="""
            人工智能在医疗领域有广泛应用，包括疾病诊断、药物研发和医疗影像分析。
            机器学习算法可以分析大量医疗数据，帮助医生做出更准确的诊断。
            深度学习在图像识别方面表现出色，特别是在医疗影像分析中。
            自然语言处理技术可以帮助分析医疗记录和研究文献。
            """,
            filename="ai_applications.txt",
            metadata={"类别": "应用", "主题": "医疗AI"}
        )
        print(f"✅ 文档2摄取成功，ID: {doc2.external_id}")

        # 等待文档索引完成
        print("⏳ 等待文档索引完成...")
        time.sleep(5)

        # 2. 构建知识图谱（这是最耗时的操作）
        print("\n🏗️  构建知识图谱（这可能需要几分钟时间）...")
        print("💡 提示：知识图谱构建包括实体提取、关系识别等复杂操作")

        start_time = time.time()

        try:
            # 使用扩展超时时间构建图谱
            graph = db.create_graph(
                name="ai_knowledge_graph",
                filters={"类别": ["技术", "应用"]},  # 包含我们刚摄取的文档
                # 注意：这里没有直接的超时参数，但会使用配置文件中的设置
            )

            build_time = time.time() - start_time
            print(f"✅ 知识图谱构建成功！耗时: {build_time:.2f}秒")
            print(f"📊 图谱统计:")
            print(f"   - 实体数量: {len(graph.entities)}")
            print(f"   - 关系数量: {len(graph.relationships)}")
            print(f"   - 文档数量: {len(graph.document_ids)}")

            # 显示一些实体示例
            print(f"\n🔍 实体示例（前5个）:")
            for i, entity in enumerate(graph.entities[:5]):
                print(f"   {i+1}. {entity.label} (类型: {entity.type})")

            # 显示一些关系示例
            print(f"\n🔗 关系示例（前5个）:")
            for i, rel in enumerate(graph.relationships[:5]):
                print(f"   {i+1}. {rel.source} -> {rel.target} ({rel.type})")

        except Exception as e:
            build_time = time.time() - start_time
            print(f"❌ 知识图谱构建失败，耗时: {build_time:.2f}秒")
            print(f"错误信息: {e}")
            print("💡 这可能是由于超时或其他技术问题导致的")
            return False

        # 3. 使用知识图谱进行查询
        print("\n🔍 使用知识图谱进行查询...")

        try:
            query_start_time = time.time()

            response = db.query(
                query="人工智能和机器学习之间是什么关系？",
                graph_name="ai_knowledge_graph",
                k=5,
                temperature=0.7
            )

            query_time = time.time() - query_start_time
            print(f"✅ 图谱查询成功！耗时: {query_time:.2f}秒")
            print(f"📄 查询结果:")
            print(response.completion)

            print(f"\n📚 使用的信息源:")
            for i, source in enumerate(response.sources):
                print(f"   {i+1}. 文档 {source.document_id}, 块 {source.chunk_number}")

        except Exception as e:
            query_time = time.time() - start_time
            print(f"❌ 图谱查询失败，耗时: {query_time:.2f}秒")
            print(f"错误信息: {e}")
            return False

        # 4. 测试图谱管理功能
        print("\n📋 测试图谱管理功能...")

        try:
            # 列出所有图谱
            graphs = db.list_graphs()
            print(f"✅ 当前共有 {len(graphs)} 个知识图谱")
            for graph in graphs:
                print(f"   - {graph.name}: {len(graph.entities)} 个实体, {len(graph.relationships)} 个关系")

        except Exception as e:
            print(f"❌ 图谱管理功能测试失败: {e}")

        db.close()
        print("\n🎉 知识图谱功能测试完成！")
        return True

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("知识图谱功能测试 - 扩展超时版本")
    print("=" * 60)
    print("💡 此测试使用了扩展的超时时间来处理知识图谱的复杂操作")
    print("⏰ 预计总耗时：5-10分钟（取决于文档复杂度和模型响应速度）")
    print()

    success = test_knowledge_graph_with_extended_timeout()

    if success:
        print("\n🎉 所有测试通过！知识图谱功能正常工作")
        print("💡 您现在可以在应用中使用知识图谱功能了")
    else:
        print("\n⚠️  测试未完全通过，但这可能是正常的")
        print("💡 知识图谱是复杂功能，可能需要根据具体需求调整参数")

    print("\n📋 超时时间配置说明:")
    print("- 客户端连接超时: 300秒（5分钟）")
    print("- 图谱构建超时: 600秒（10分钟）- 在配置文件中设置")
    print("- 图谱查询超时: 300秒（5分钟）- 在配置文件中设置")
    print("- 数据库查询超时: 300秒（5分钟）- 在配置文件中设置")

if __name__ == "__main__":
    main()
