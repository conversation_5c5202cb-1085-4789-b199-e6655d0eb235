# 优化的 Dockerfile - 避免 VPN 网络问题
FROM python:3.11.12-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 设置工作目录
WORKDIR /app

# 更新包管理器并安装基础依赖（最小化网络请求）
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 升级 pip
RUN pip install --no-cache-dir --upgrade pip

# 复制 requirements 文件
COPY requirements.txt .

# 分批安装 Python 依赖，避免大量并发网络请求
RUN pip install --no-cache-dir \
    fastapi==0.111.0 \
    uvicorn==0.34.2 \
    python-dotenv==1.0.1

RUN pip install --no-cache-dir \
    sqlalchemy==2.0.40 \
    alembic==1.14.0 \
    psycopg2-binary==2.9.9 \
    redis==5.2.1

RUN pip install --no-cache-dir \
    pydantic==2.11.4 \
    pydantic-settings==2.9.1 \
    httpx==0.28.1

RUN pip install --no-cache-dir \
    openai==1.75.0 \
    litellm==1.69.1 \
    instructor==1.8.1

RUN pip install --no-cache-dir \
    ollama==0.4.8 \
    requests==2.32.3 \
    toml==0.10.2

# 安装其他必要依赖
RUN pip install --no-cache-dir \
    python-multipart==0.0.20 \
    itsdangerous==2.2.0 \
    arq==0.26.3 \
    PyJWT==2.8.0 \
    google-generativeai==0.8.5

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/logs /app/storage

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "-m", "uvicorn", "core.api:app", "--host", "0.0.0.0", "--port", "8000"]
