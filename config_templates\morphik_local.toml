# Morphik 本地模型配置模板
# 使用本地 Ollama 模型

[completion]
model = "ollama_qwen3_4b"
default_max_tokens = "1000"
default_temperature = 0.5

[embedding]
model = "ollama_embedding_local"
dimensions = 768
similarity_metric = "cosine"

[agent]
model = "ollama_qwen3_4b"

[document_analysis]
model = "ollama_qwen3_4b"

[parser.vision]
model = "ollama_qwen3_4b"

[rules]
model = "ollama_qwen3_4b"
batch_size = 4096

[graph]
model = "ollama_qwen3_4b"
enable_entity_resolution = true

[contextual_chunking]
model = "ollama_qwen3_4b"
