# Morphik 服务启动教程

## 📋 目录
- [概述](#概述)
- [前置要求](#前置要求)
- [快速启动](#快速启动)
- [服务管理](#服务管理)
- [服务访问](#服务访问)
- [故障排除](#故障排除)
- [常见问题](#常见问题)

## 概述

本教程将指导您如何部署和管理 Morphik 项目，包括：
- 使用 Docker Compose 部署基础服务
- 配置本地 Ollama 服务器
- 使用 qwen2.5 作为主要大语言模型
- 使用 nomic-embed-text:latest 作为嵌入模型

## 前置要求

### 系统要求
- macOS / Linux / Windows
- Docker Desktop
- Python 3.9+
- 至少 8GB 内存
- 至少 10GB 可用磁盘空间

### 软件依赖
```bash
# 检查 Docker 版本
docker --version

# 检查 Python 版本
python3 --version

# 检查 pip 版本
pip3 --version
```

## 快速启动

### 1. 克隆项目
```bash
git clone <morphik-repository>
cd morphik-core_main
```

### 2. 一键启动所有服务
```bash
# 给脚本执行权限
chmod +x manage_services.sh

# 启动所有服务
./manage_services.sh start-all
```

### 3. 验证服务状态
```bash
# 检查服务状态
./manage_services.sh status
```

## 服务管理

### 服务管理脚本

我们提供了一个完整的服务管理脚本 `manage_services.sh`，支持以下命令：

| 命令 | 功能 | 说明 |
|------|------|------|
| `start-all` | 启动所有服务 | 启动 PostgreSQL、Redis、Ollama 和 Morphik API |
| `start-base` | 启动基础服务 | 只启动 PostgreSQL、Redis、Ollama |
| `start-api` | 启动 API 服务 | 只启动 Morphik API（需要基础服务运行） |
| `stop` | 停止所有服务 | 停止所有 Docker 容器和 Python 进程 |
| `restart` | 重启所有服务 | 先停止再启动所有服务 |
| `status` | 查看服务状态 | 显示详细的服务运行状态 |
| `logs` | 查看服务日志 | 显示 Docker 容器日志 |
| `help` | 显示帮助 | 显示所有可用命令 |

### 常用操作

#### 启动服务
```bash
# 启动所有服务
./manage_services.sh start-all

# 只启动基础服务
./manage_services.sh start-base

# 只启动 API 服务
./manage_services.sh start-api
```

#### 停止服务
```bash
# 停止所有服务
./manage_services.sh stop
```

#### 重启服务
```bash
# 重启所有服务
./manage_services.sh restart
```

#### 查看状态
```bash
# 查看服务状态
./manage_services.sh status

# 查看服务日志
./manage_services.sh logs
```

## 服务访问

### 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| PostgreSQL | 5433 | 数据库服务（带 pgvector 扩展） |
| Redis | 6379 | 缓存服务 |
| Ollama | 11435 | AI 模型服务 |
| Morphik API | 8000 | 主要 API 服务 |

### API 访问地址

启动成功后，您可以访问以下地址：

- **主 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **配置信息**: http://localhost:8000/config

### API 测试

#### 健康检查
```bash
curl http://localhost:8000/health
```

#### 聊天测试
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "你好，请介绍一下你自己", "model": "qwen2.5"}'
```

## 故障排除

### 常见问题及解决方案

#### 1. Docker 未启动
**错误**: `Cannot connect to the Docker daemon`
**解决**: 启动 Docker Desktop

#### 2. 端口被占用
**错误**: `Port already in use`
**解决**:
```bash
# 查看端口占用
lsof -i :8000

# 停止占用进程
./manage_services.sh stop
```

#### 3. 模型下载失败
**错误**: Ollama 模型下载超时
**解决**:
```bash
# 手动下载模型
curl -X POST http://localhost:11435/api/pull -d '{"name": "qwen2.5"}'
curl -X POST http://localhost:11435/api/pull -d '{"name": "nomic-embed-text:latest"}'
```

#### 4. 数据库连接失败
**错误**: `Connection to database failed`
**解决**:
```bash
# 重启数据库服务
./manage_services.sh restart
```

### 日志查看

```bash
# 查看所有服务日志
./manage_services.sh logs

# 查看特定容器日志
docker compose logs postgres
docker compose logs redis
docker compose logs ollama
```

## 常见问题

### Q: 如何更换 AI 模型？
A: 编辑 `morphik.toml` 配置文件，修改 `completion.model_name` 和 `embedding.model_name` 字段。

### Q: 如何持久化数据？
A: 数据会自动保存在 Docker volumes 中，重启服务不会丢失数据。

### Q: 如何备份数据？
A:
```bash
# 备份 PostgreSQL 数据
docker exec morphik-core_main-postgres-1 pg_dump -U morphik morphik > backup.sql

# 恢复数据
docker exec -i morphik-core_main-postgres-1 psql -U morphik morphik < backup.sql
```

### Q: 如何完全清理环境？
A:
```bash
# 停止所有服务
./manage_services.sh stop

# 删除所有容器和数据
docker compose down -v

# 删除镜像（可选）
docker system prune -a
```

### Q: 如何更新服务？
A:
```bash
# 拉取最新代码
git pull

# 重新构建并启动
./manage_services.sh restart
```

## 技术支持

如果遇到问题，请：
1. 首先查看 `./manage_services.sh status` 输出
2. 检查 `./manage_services.sh logs` 中的错误信息
3. 尝试 `./manage_services.sh restart` 重启服务
4. 查看本文档的故障排除部分

## 高级配置

### 环境变量配置

创建 `.env` 文件来自定义配置：

```bash
# 数据库配置
POSTGRES_DB=morphik
POSTGRES_USER=morphik
POSTGRES_PASSWORD=morphik
POSTGRES_PORT=5433

# Redis 配置
REDIS_PORT=6379

# Ollama 配置
OLLAMA_PORT=11435

# API 配置
API_PORT=8000
API_HOST=0.0.0.0
```

### 模型配置

编辑 `morphik.toml` 文件：

```toml
[api]
host = "0.0.0.0"
port = 8000

[database]
provider = "postgres"
host = "localhost"
port = 5433
name = "morphik"
user = "morphik"
password = "morphik"

[completion]
provider = "ollama"
model_name = "qwen2.5"
base_url = "http://localhost:11435"

[embedding]
provider = "ollama"
model_name = "nomic-embed-text:latest"
base_url = "http://localhost:11435"
```

### Docker Compose 配置

主要的 `docker-compose.yml` 配置说明：

```yaml
services:
  postgres:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_DB: morphik
      POSTGRES_USER: morphik
      POSTGRES_PASSWORD: morphik
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U morphik"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11435:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
```

## 性能优化

### 系统资源配置

#### 内存优化
```bash
# 为 Docker 分配更多内存（推荐 8GB+）
# 在 Docker Desktop 设置中调整

# 为 Ollama 设置内存限制
export OLLAMA_MAX_LOADED_MODELS=2
export OLLAMA_MAX_QUEUE=512
```

#### 存储优化
```bash
# 清理未使用的 Docker 资源
docker system prune -f

# 清理未使用的模型
docker exec morphik-core_main-ollama-1 ollama rm <unused-model>
```

### 模型管理

#### 查看已安装模型
```bash
curl http://localhost:11435/api/tags
```

#### 下载新模型
```bash
# 下载其他模型
curl -X POST http://localhost:11435/api/pull -d '{"name": "llama2"}'
curl -X POST http://localhost:11435/api/pull -d '{"name": "codellama"}'
```

#### 删除模型
```bash
curl -X DELETE http://localhost:11435/api/delete -d '{"name": "model-name"}'
```

## 开发指南

### 本地开发环境

#### 1. 安装开发依赖
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 2. 运行开发服务器
```bash
# 启动基础服务
./manage_services.sh start-base

# 运行开发服务器
uvicorn morphik.api.main:app --reload --host 0.0.0.0 --port 8000
```

### API 开发

#### 添加新的 API 端点
```python
from fastapi import APIRouter

router = APIRouter()

@router.get("/new-endpoint")
async def new_endpoint():
    return {"message": "Hello from new endpoint"}
```

#### 数据库迁移
```bash
# 生成迁移文件
alembic revision --autogenerate -m "Add new table"

# 执行迁移
alembic upgrade head
```

## 监控和日志

### 服务监控

#### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

services=("postgres:5433" "redis:6379" "ollama:11435" "api:8000")

for service in "${services[@]}"; do
    name=${service%:*}
    port=${service#*:}

    if nc -z localhost $port; then
        echo "✅ $name is running on port $port"
    else
        echo "❌ $name is not responding on port $port"
    fi
done
```

#### 日志轮转
```bash
# 配置 Docker 日志轮转
# 在 docker-compose.yml 中添加：
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 性能监控

#### 资源使用情况
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
docker system df

# 查看网络使用
docker network ls
```

## 安全配置

### 网络安全

#### 防火墙配置
```bash
# 只允许本地访问（生产环境建议）
# 修改 docker-compose.yml 中的端口映射：
ports:
  - "127.0.0.1:8000:8000"  # 只绑定到本地
```

#### SSL/TLS 配置
```bash
# 使用 nginx 反向代理配置 HTTPS
# 创建 nginx.conf
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 数据安全

#### 数据库备份自动化
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec morphik-core_main-postgres-1 pg_dump -U morphik morphik > $BACKUP_DIR/morphik_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/morphik_$DATE.sql

# 删除 7 天前的备份
find $BACKUP_DIR -name "morphik_*.sql.gz" -mtime +7 -delete

echo "Backup completed: morphik_$DATE.sql.gz"
```

---

**最后更新**: 2024年12月
**版本**: 1.0.0
