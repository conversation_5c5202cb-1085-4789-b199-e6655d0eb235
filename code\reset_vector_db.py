#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重置向量数据库脚本
用于在更改嵌入模型维度时重建向量表
"""

import asyncio
import asyncpg
import sys

async def reset_vector_database():
    """重置向量数据库表以匹配新的维度"""
    print("🔄 重置向量数据库...")

    try:
        # 连接到 PostgreSQL
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")

        # 删除现有的向量表
        print("🗑️  删除现有向量表...")
        await conn.execute("DROP TABLE IF EXISTS vector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS multivector_embeddings CASCADE;")
        print("✅ 已删除现有向量表")

        # 创建新的向量表（1024维）
        print("🏗️  创建新的向量表（1024维）...")

        # 创建向量嵌入表
        await conn.execute("""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # 创建多向量嵌入表
        await conn.execute("""
            CREATE TABLE multivector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)

        # 创建索引
        print("📊 创建向量索引...")
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_document_id
            ON vector_embeddings(document_id);
        """)

        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_embedding
            ON vector_embeddings USING ivfflat (embedding vector_cosine_ops)
            WITH (lists = 100);
        """)

        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_document_id
            ON multivector_embeddings(document_id);
        """)

        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_embedding
            ON multivector_embeddings USING ivfflat (embedding vector_cosine_ops)
            WITH (lists = 100);
        """)

        print("✅ 已创建新的向量表和索引")

        await conn.close()
        print("🎉 向量数据库重置完成！")
        print("📝 注意：所有现有的向量数据已被删除，需要重新摄取文档")

    except Exception as e:
        print(f"❌ 重置向量数据库时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(reset_vector_database())
