#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
强制设置1024维向量数据库
解决 Morphik 维度变更的交互式确认问题
"""

import asyncio
import asyncpg
import sys
import os

async def force_1024_dimensions():
    """强制创建1024维向量表并更新元数据"""
    print("🔧 强制设置1024维向量数据库...")
    
    try:
        # 连接到 PostgreSQL
        conn = await asyncpg.connect(
            host="localhost",
            port=5433,
            user="morphik",
            password="morphik",
            database="morphik"
        )
        print("✅ 已连接到 PostgreSQL")
        
        # 1. 删除所有现有的向量相关表
        print("🗑️  删除所有现有向量表...")
        await conn.execute("DROP TABLE IF EXISTS vector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS multivector_embeddings CASCADE;")
        await conn.execute("DROP TABLE IF EXISTS vector_store_metadata CASCADE;")
        print("✅ 已删除所有向量表")
        
        # 2. 创建新的1024维向量表
        print("🏗️  创建新的1024维向量表...")
        
        # 创建向量嵌入表
        await conn.execute("""
            CREATE TABLE vector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        print("✅ 创建 vector_embeddings 表成功")
        
        # 创建多向量嵌入表
        await conn.execute("""
            CREATE TABLE multivector_embeddings (
                id SERIAL PRIMARY KEY,
                document_id VARCHAR NOT NULL,
                chunk_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                chunk_metadata VARCHAR,
                embedding vector(1024) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        print("✅ 创建 multivector_embeddings 表成功")
        
        # 3. 创建向量存储元数据表（记录维度信息）
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS vector_store_metadata (
                id SERIAL PRIMARY KEY,
                key VARCHAR UNIQUE NOT NULL,
                value VARCHAR NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        print("✅ 创建 vector_store_metadata 表成功")
        
        # 4. 插入维度元数据
        await conn.execute("""
            INSERT INTO vector_store_metadata (key, value) 
            VALUES ('dimensions', '1024') 
            ON CONFLICT (key) DO UPDATE SET 
                value = EXCLUDED.value,
                updated_at = CURRENT_TIMESTAMP;
        """)
        print("✅ 设置维度元数据为1024")
        
        # 5. 创建索引
        print("📊 创建向量索引...")
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_document_id 
            ON vector_embeddings(document_id);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_vector_embeddings_embedding 
            ON vector_embeddings USING ivfflat (embedding vector_cosine_ops) 
            WITH (lists = 100);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_document_id 
            ON multivector_embeddings(document_id);
        """)
        
        await conn.execute("""
            CREATE INDEX idx_multivector_embeddings_embedding 
            ON multivector_embeddings USING ivfflat (embedding vector_cosine_ops) 
            WITH (lists = 100);
        """)
        
        print("✅ 已创建所有索引")
        
        # 6. 验证设置
        print("🔍 验证设置...")
        result = await conn.fetchrow("""
            SELECT value FROM vector_store_metadata WHERE key = 'dimensions';
        """)
        
        if result and result['value'] == '1024':
            print(f"✅ 维度元数据设置正确: {result['value']}")
        else:
            print("❌ 维度元数据设置失败")
        
        # 验证表结构
        tables = await conn.fetch("""
            SELECT table_name, column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name IN ('vector_embeddings', 'multivector_embeddings')
            AND column_name = 'embedding';
        """)
        
        for table in tables:
            print(f"✅ {table['table_name']}.{table['column_name']}: {table['data_type']}")
        
        await conn.close()
        print("🎉 1024维向量数据库设置完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置向量数据库时出错: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(force_1024_dimensions())
    if success:
        print("\n🎯 现在可以重启 Morphik 服务了")
        print("💡 运行: ./code/manage_services.sh restart")
    else:
        sys.exit(1)
